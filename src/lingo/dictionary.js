export default {
  version: 0.1,
  files: {
    "app/auth/forgot-password/page.tsx": {
      entries: {
        "14/declaration/body/5/argument/1/1/1": {
          content: {
            en: "<element:HelpCircle></element:HelpCircle> Forgot Your Password?",
          },
          hash: "019e94b7ec07e47f2900fb91bc475319",
        },
        "14/declaration/body/5/argument/1/1/3": {
          content: {
            en: "Enter your email address and we will send you a link to reset your password.",
          },
          hash: "70dd5da5e78eb0f54ed073ae3fee6949",
        },
        "14/declaration/body/5/argument/1/3/1/1/1/expression/alternate/3": {
          content: {
            en: "If an account exists for <function:form.getValues/>, you will receive an email with password reset instructions shortly.",
          },
          hash: "a85848aed29550a81cf7e62e9cfe53d5",
        },
        "14/declaration/body/5/argument/1/3/1/1/1/expression/consequent/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Email",
            },
            hash: "e7f34943a0c2fb849db1839ff6ef5cb5",
          },
        "14/declaration/body/5/argument/1/3/1/1/1/expression/consequent/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "<EMAIL>",
            },
            hash: "b13d316cc2609fffd0739c45190b6787",
          },
        "14/declaration/body/5/argument/1/3/1/3/1/expression/right": {
          content: {
            en: "<expression/> Send Reset Link",
          },
          hash: "b0a1647eff823ec5e24ae89b89962765",
        },
        "14/declaration/body/5/argument/1/3/1/3/3": {
          content: {
            en: "<element:ArrowLeft></element:ArrowLeft> Back to Login",
          },
          hash: "7993cae80d476c6bb24854cd6f4cb90b",
        },
      },
    },
    "app/auth/login/page.tsx": {
      entries: {
        "15/body/6/argument/1/1/1": {
          content: {
            en: "<element:KeyIcon></element:KeyIcon> Welcome back",
          },
          hash: "783ff91c8cbcf9c1ed18f3009bacdb7a",
        },
        "15/body/6/argument/1/1/3": {
          content: {
            en: "Sign in to your Budget Tracker account",
          },
          hash: "12d698b22c428afef91ed44ea7e0773b",
        },
        "15/body/6/argument/1/3/1/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Email or Username",
            },
            hash: "a7a5617eb5d02a1dea2ec6f189c941c4",
          },
        "15/body/6/argument/1/3/1/1/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "<EMAIL> or johndoe123",
            },
            hash: "a279a308b393261f7411d17eb3f50f53",
          },
        "15/body/6/argument/1/3/1/1/1/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "You can login with either your email address or username",
            },
            hash: "a0626a4b2ee574ddd91fa8e212758c03",
          },
        "15/body/6/argument/1/3/1/1/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Password",
            },
            hash: "223a61cf906ab9c40d22612c588dff48",
          },
        "15/body/6/argument/1/3/1/1/5/1": {
          content: {
            en: "Forgot Password?",
          },
          hash: "fda0832ee767ddc76082edf594bd62e7",
        },
        "15/body/6/argument/1/3/1/3/1": {
          content: {
            en: "<expression/> Sign In",
          },
          hash: "60808ef03d6b56c87b7eed3e768bbfbb",
        },
        "15/body/6/argument/1/3/1/3/3": {
          content: {
            en: "Don't have an account? <element:Link>Sign up</element:Link>",
          },
          hash: "43acd339fc452f29abe44fb4796bd887",
        },
        "16/declaration/body/0/argument/openingElement/0/value/expression/1/1/1":
          {
            content: {
              en: "<element:KeyIcon></element:KeyIcon> Welcome back",
            },
            hash: "fdd1f3cb6d58aaafc9bbd844f168ab96",
          },
        "16/declaration/body/0/argument/openingElement/0/value/expression/1/1/3":
          {
            content: {
              en: "Loading...",
            },
            hash: "041ed632904cd5c35017ec8bc93b3b0f",
          },
      },
    },
    "app/auth/set-password/page.tsx": {
      entries: {
        "15/body/11/argument/1/1/1": {
          content: {
            en: "<element:Key></element:Key> Set Your Password",
          },
          hash: "802f7164498c5be11a560bf7864168e8",
        },
        "15/body/11/argument/1/1/3/1/expression/consequent": {
          content: {
            en: "<element:Users></element:Users> Complete your account setup to join the family group",
          },
          hash: "c6198e7a14777bf00c10af0abb4c9613",
        },
        "15/body/11/argument/1/3/1/1/1/1/1": {
          content: {
            en: "Welcome, {user.email}!",
          },
          hash: "ddad7f0baf16e2baa11cf622335bc85a",
        },
        "15/body/11/argument/1/3/1/1/1/3": {
          content: {
            en: "Please set a secure password for your account.",
          },
          hash: "3f15171101e910e0fcbc274eab8fae4e",
        },
        "15/body/11/argument/1/3/1/1/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Password",
            },
            hash: "223a61cf906ab9c40d22612c588dff48",
          },
        "15/body/11/argument/1/3/1/1/3/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Must be at least 8 characters with uppercase, lowercase, and numbers",
            },
            hash: "20ec652b760202de67f31ead631ea573",
          },
        "15/body/11/argument/1/3/1/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Confirm Password",
            },
            hash: "140bae95eb2040668118c6244c4a4825",
          },
        "15/body/11/argument/1/3/1/3/1": {
          content: {
            en: "<expression/> Complete Setup",
          },
          hash: "90d49b8c810a740afae91213a8d490d6",
        },
        "15/body/9/consequent/0/argument/1/3": {
          content: {
            en: "Setting up your account...",
          },
          hash: "84d67218df7efc93b01365aac669074e",
        },
        "16/declaration/body/0/argument/openingElement/0/value/expression/1/3":
          {
            content: {
              en: "Setting up your account...",
            },
            hash: "84d67218df7efc93b01365aac669074e",
          },
      },
    },
    "app/auth/signup/page.tsx": {
      entries: {
        "15/body/6/argument/1/1/1": {
          content: {
            en: "<element:Edit3></element:Edit3> Create an account",
          },
          hash: "3436faf27282b6de14fd5ff8a6e486f4",
        },
        "15/body/6/argument/1/1/3": {
          content: {
            en: "Enter your information to join Budget Tracker",
          },
          hash: "451b4617435e012266186812d24c78b3",
        },
        "15/body/6/argument/1/3/1/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Username",
            },
            hash: "2ee65bc2dd2f12cf2672f95b2a054bf8",
          },
        "15/body/6/argument/1/3/1/1/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "johndoe123",
            },
            hash: "af03b2ae170971a0dd6758a962add10b",
          },
        "15/body/6/argument/1/3/1/1/1/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "This will be your unique identifier on the platform",
            },
            hash: "89def2aba45ac1592aa68652e39eaf13",
          },
        "15/body/6/argument/1/3/1/1/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Full Name",
            },
            hash: "ba3094a10db2bb9234cb7e0dc86e40fd",
          },
        "15/body/6/argument/1/3/1/1/3/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "John Doe",
            },
            hash: "febee8e9ab40b2fe5106d72675228d00",
          },
        "15/body/6/argument/1/3/1/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Email",
            },
            hash: "e7f34943a0c2fb849db1839ff6ef5cb5",
          },
        "15/body/6/argument/1/3/1/1/5/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "<EMAIL>",
            },
            hash: "b13d316cc2609fffd0739c45190b6787",
          },
        "15/body/6/argument/1/3/1/1/7/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Password",
            },
            hash: "223a61cf906ab9c40d22612c588dff48",
          },
        "15/body/6/argument/1/3/1/1/7/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Must be at least 8 characters with uppercase, lowercase, and numbers",
            },
            hash: "20ec652b760202de67f31ead631ea573",
          },
        "15/body/6/argument/1/3/1/3/1": {
          content: {
            en: "<expression/> Create Account",
          },
          hash: "f9b2857f1b04dbbb995f1030e5e469bb",
        },
        "15/body/6/argument/1/3/1/3/3": {
          content: {
            en: "Already have an account? <element:Link>Sign in</element:Link>",
          },
          hash: "793324a3be14dc91aa2d6e36e2632cd7",
        },
        "16/declaration/body/0/argument/openingElement/0/value/expression/1/1/1":
          {
            content: {
              en: "<element:Edit3></element:Edit3> Create an account",
            },
            hash: "c43fec3b9fa85f68bbe29f17ad64895e",
          },
        "16/declaration/body/0/argument/openingElement/0/value/expression/1/1/3":
          {
            content: {
              en: "Loading...",
            },
            hash: "041ed632904cd5c35017ec8bc93b3b0f",
          },
      },
    },
    "app/auth/update-password/page.tsx": {
      entries: {
        "14/body/10/argument/1/1/1": {
          content: {
            en: "<element:ShieldCheck></element:ShieldCheck> Update Your Password",
          },
          hash: "b17a609c8079f0485f3772a5b6914db1",
        },
        "14/body/10/argument/1/1/3": {
          content: {
            en: "Enter your new password below. Make sure it is strong and memorable.",
          },
          hash: "e701897ecc86b7d786c0f5b6dc48d3a2",
        },
        "14/body/10/argument/1/3/1/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "New Password",
            },
            hash: "80d809edd5d1b66123b18faaac10537b",
          },
        "14/body/10/argument/1/3/1/1/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "••••••••••",
            },
            hash: "6ae5e2038d175fd39f36bc6bcdc54a0b",
          },
        "14/body/10/argument/1/3/1/1/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Confirm New Password",
            },
            hash: "d3194bdcb74c9a912c67d7e1b030b88a",
          },
        "14/body/10/argument/1/3/1/1/3/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "••••••••••",
            },
            hash: "6ae5e2038d175fd39f36bc6bcdc54a0b",
          },
        "14/body/10/argument/1/3/1/3/1": {
          content: {
            en: "<expression/> Update Password",
          },
          hash: "33f61573b81272349b2df6f5d80d7cc8",
        },
        "14/body/10/argument/1/3/1/3/3": {
          content: {
            en: "<element:ArrowLeft></element:ArrowLeft> Back to Login",
          },
          hash: "7993cae80d476c6bb24854cd6f4cb90b",
        },
        "14/body/9/consequent/0/argument/1/1/1": {
          content: {
            en: "<element:AlertCircle></element:AlertCircle> Invalid or Expired Link",
          },
          hash: "f072d1a049d08f3bc1942ab983147721",
        },
        "14/body/9/consequent/0/argument/1/3/1": {
          content: {
            en: "The password reset link is invalid or has expired. This can happen if the link has already been used or if too much time has passed since it was requested.",
          },
          hash: "d73066cae886a315963f138de0f18005",
        },
        "14/body/9/consequent/0/argument/1/5/1": {
          content: {
            en: "<element:ArrowLeft></element:ArrowLeft> Request a New Reset Link",
          },
          hash: "c1b6676ec034341388b620b8b9fd3e2b",
        },
      },
    },
    "app/beta/auth/forgot-password/page.tsx": {
      entries: {
        "10/declaration/body/6/consequent/0/argument/1/1/1/1/1": {
          content: {
            en: "BT",
          },
          hash: "b11b5c1a2d5674b439048448bf1db3a5",
        },
        "10/declaration/body/6/consequent/0/argument/1/1/1/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> BETA",
          },
          hash: "8153f4c4ad64ceed0f3b67751ce23be0",
        },
        "10/declaration/body/6/consequent/0/argument/1/1/3": {
          content: {
            en: "Check Your Email",
          },
          hash: "128454044f09acb189bc9429173c3fe0",
        },
        "10/declaration/body/6/consequent/0/argument/1/1/5": {
          content: {
            en: "We've sent password reset instructions to your email",
          },
          hash: "aa43a47dfd4a4c5fb56f97b067a811fe",
        },
        "10/declaration/body/6/consequent/0/argument/1/3/1/3": {
          content: {
            en: "If an account with that email exists, you'll receive password reset instructions shortly.",
          },
          hash: "0f628d580ad8a765a7d7f040f43bbbdf",
        },
        "10/declaration/body/6/consequent/0/argument/1/3/1/5/1/1": {
          content: {
            en: "<element:ArrowLeft></element:ArrowLeft> Back to Login",
          },
          hash: "1000de4b3b8d8a500126954bf8bf0e6d",
        },
        "10/declaration/body/7/argument/1/3/1/1/1": {
          content: {
            en: "BT",
          },
          hash: "b11b5c1a2d5674b439048448bf1db3a5",
        },
        "10/declaration/body/7/argument/1/3/1/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> BETA",
          },
          hash: "2875edb8f30d3d9d8a85d3784f089859",
        },
        "10/declaration/body/7/argument/1/3/3": {
          content: {
            en: "Reset Your Password",
          },
          hash: "dd55e85b15c0978aa6227cba0ef3e539",
        },
        "10/declaration/body/7/argument/1/3/5": {
          content: {
            en: "Enter your email to receive reset instructions",
          },
          hash: "afaabf7f139ea11f0ec9ddb47ebd5d72",
        },
        "10/declaration/body/7/argument/1/5/1/1": {
          content: {
            en: "Forgot Password",
          },
          hash: "582bbe27bacf16dd5823a028197138c9",
        },
        "10/declaration/body/7/argument/1/5/1/3": {
          content: {
            en: "We'll send you a link to reset your password",
          },
          hash: "90ade76e4b5d89b363cc39ed144330e9",
        },
        "10/declaration/body/7/argument/1/5/3/1/1/1": {
          content: {
            en: "Email",
          },
          hash: "e7f34943a0c2fb849db1839ff6ef5cb5",
        },
        "10/declaration/body/7/argument/1/5/3/1/1/3/3-placeholder": {
          content: {
            en: "Enter your email",
          },
          hash: "39931962707c99b99a5a073ab579396b",
        },
        "10/declaration/body/7/argument/1/5/3/3/1": {
          content: {
            en: "<element:ArrowLeft></element:ArrowLeft> Back to Login",
          },
          hash: "7993cae80d476c6bb24854cd6f4cb90b",
        },
        "10/declaration/body/7/argument/1/7/1": {
          content: {
            en: "← Back to Beta Landing",
          },
          hash: "f21835392835ecbe21183a3c9855d77a",
        },
      },
    },
    "app/beta/auth/login/page.tsx": {
      entries: {
        "12/declaration/body/7/argument/1/3/1/1/1": {
          content: {
            en: "BT",
          },
          hash: "b11b5c1a2d5674b439048448bf1db3a5",
        },
        "12/declaration/body/7/argument/1/3/1/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> BETA",
          },
          hash: "2875edb8f30d3d9d8a85d3784f089859",
        },
        "12/declaration/body/7/argument/1/3/3": {
          content: {
            en: "Beta Program Login",
          },
          hash: "4ee234063085a323ecdb6668c7733bc4",
        },
        "12/declaration/body/7/argument/1/3/5": {
          content: {
            en: "Sign in to access your exclusive beta features",
          },
          hash: "bcf3ca1c495d767e1204c02f51e0dbea",
        },
        "12/declaration/body/7/argument/1/3/7/1": {
          content: {
            en: "This is the beta version. Looking for the main app? <element:Link>Sign in to main app</element:Link>",
          },
          hash: "fa80c37df7a707395431e470d5c8f0c4",
        },
        "12/declaration/body/7/argument/1/5/1/1": {
          content: {
            en: "Welcome Back to Beta",
          },
          hash: "86a153a8d2bec77c0f6bfe9bacee82e1",
        },
        "12/declaration/body/7/argument/1/5/1/3": {
          content: {
            en: "Enter your beta account credentials to continue",
          },
          hash: "add963ea5807e547b389700fa39f1032",
        },
        "12/declaration/body/7/argument/1/5/3/1/1/1": {
          content: {
            en: "Email",
          },
          hash: "e7f34943a0c2fb849db1839ff6ef5cb5",
        },
        "12/declaration/body/7/argument/1/5/3/1/1/3/3-placeholder": {
          content: {
            en: "Enter your email",
          },
          hash: "39931962707c99b99a5a073ab579396b",
        },
        "12/declaration/body/7/argument/1/5/3/1/3/1": {
          content: {
            en: "Password",
          },
          hash: "223a61cf906ab9c40d22612c588dff48",
        },
        "12/declaration/body/7/argument/1/5/3/1/3/3/3-placeholder": {
          content: {
            en: "Enter your password",
          },
          hash: "ea4fdd034522dead21bae0c0abb52eae",
        },
        "12/declaration/body/7/argument/1/5/3/1/5/1": {
          content: {
            en: "Forgot password?",
          },
          hash: "8e5140d59e2d3e31ae0ad5062cc249c3",
        },
        "12/declaration/body/7/argument/1/5/3/3/1": {
          content: {
            en: "Don't have beta access yet? <element:Link>Join the beta program</element:Link>",
          },
          hash: "42732dc2bb98697616e90885c8b628c5",
        },
        "12/declaration/body/7/argument/1/5/3/3/3": {
          content: {
            en: "Need to access the main app? <element:Link>Main app login</element:Link>",
          },
          hash: "98fdafeecc1825fb5c55f665669c7e08",
        },
        "12/declaration/body/7/argument/1/7/1": {
          content: {
            en: "← Back to Beta Landing",
          },
          hash: "f21835392835ecbe21183a3c9855d77a",
        },
        "12/declaration/body/7/argument/1/7/3": {
          content: {
            en: "← Back to Main Site",
          },
          hash: "47791712fb85dccfd4e6cb84126617ce",
        },
      },
    },
    "app/beta/auth/signup/page.tsx": {
      entries: {
        "13/declaration/body/7/argument/1/3/1/1/1": {
          content: {
            en: "BT",
          },
          hash: "b11b5c1a2d5674b439048448bf1db3a5",
        },
        "13/declaration/body/7/argument/1/3/1/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> BETA",
          },
          hash: "2875edb8f30d3d9d8a85d3784f089859",
        },
        "13/declaration/body/7/argument/1/3/3": {
          content: {
            en: "Join the Beta Program",
          },
          hash: "8200cdb508ceb5d546e078cde65edfb8",
        },
        "13/declaration/body/7/argument/1/3/5": {
          content: {
            en: "Get exclusive early access to revolutionary financial tools",
          },
          hash: "f9cb058dafc919070dc1b4a3cd24979b",
        },
        "13/declaration/body/7/argument/1/3/7/1": {
          content: {
            en: "This is the beta signup. Looking for the main app? <element:Link>Sign up for main app</element:Link>",
          },
          hash: "ae598e1116bd1e4da0e32cbadeaa5688",
        },
        "13/declaration/body/7/argument/1/5/1/1": {
          content: {
            en: "Create Beta Account",
          },
          hash: "4b423ed57524fada668051c8f1c99a31",
        },
        "13/declaration/body/7/argument/1/5/1/3": {
          content: {
            en: "Sign up to start your exclusive beta experience",
          },
          hash: "9ac850999e6a41f86ebfd24221a530c7",
        },
        "13/declaration/body/7/argument/1/5/3/1/1/1": {
          content: {
            en: "Full Name",
          },
          hash: "ba3094a10db2bb9234cb7e0dc86e40fd",
        },
        "13/declaration/body/7/argument/1/5/3/1/1/3/3-placeholder": {
          content: {
            en: "Enter your full name",
          },
          hash: "5c927d18c76f612d106a4b69e437f6be",
        },
        "13/declaration/body/7/argument/1/5/3/1/3/1": {
          content: {
            en: "Email",
          },
          hash: "e7f34943a0c2fb849db1839ff6ef5cb5",
        },
        "13/declaration/body/7/argument/1/5/3/1/3/3/3-placeholder": {
          content: {
            en: "Enter your email",
          },
          hash: "39931962707c99b99a5a073ab579396b",
        },
        "13/declaration/body/7/argument/1/5/3/1/5/1": {
          content: {
            en: "Password",
          },
          hash: "223a61cf906ab9c40d22612c588dff48",
        },
        "13/declaration/body/7/argument/1/5/3/1/5/3/3-placeholder": {
          content: {
            en: "Create a strong password",
          },
          hash: "0d61743541fa5e1758c89d8207d1a8e9",
        },
        "13/declaration/body/7/argument/1/5/3/1/7/1": {
          content: {
            en: "Confirm Password",
          },
          hash: "140bae95eb2040668118c6244c4a4825",
        },
        "13/declaration/body/7/argument/1/5/3/1/7/3/3-placeholder": {
          content: {
            en: "Confirm your password",
          },
          hash: "224b4aa56991875af1df9d3b102de647",
        },
        "13/declaration/body/7/argument/1/5/3/1/9/1/3": {
          content: {
            en: "I want to participate in the beta program and receive updates",
          },
          hash: "ddf6a0a4bb9383f9f5c81ae443dc12fc",
        },
        "13/declaration/body/7/argument/1/5/3/1/9/3/3": {
          content: {
            en: "I agree to the <element:Link>Terms of Service</element:Link>  and <element:Link>Privacy Policy</element:Link>",
          },
          hash: "fef7c60b3f4a542d8c8e31a7efda7352",
        },
        "13/declaration/body/7/argument/1/5/3/3/1": {
          content: {
            en: "Already have a beta account? <element:Link>Sign in to beta</element:Link>",
          },
          hash: "84da1ebe4428d3a1738ae0404c6ef9f5",
        },
        "13/declaration/body/7/argument/1/5/3/3/3": {
          content: {
            en: "Want to use the main app instead? <element:Link>Main app signup</element:Link>",
          },
          hash: "eee9091479f03e4a157060ad1a39391f",
        },
        "13/declaration/body/7/argument/1/7/1": {
          content: {
            en: "← Back to Beta Landing",
          },
          hash: "f21835392835ecbe21183a3c9855d77a",
        },
        "13/declaration/body/7/argument/1/7/3": {
          content: {
            en: "← Back to Main Site",
          },
          hash: "47791712fb85dccfd4e6cb84126617ce",
        },
      },
    },
    "app/beta/auth/verify-email/page.tsx": {
      entries: {
        "6/declaration/body/0/argument/1/3/1/1/1": {
          content: {
            en: "BT",
          },
          hash: "b11b5c1a2d5674b439048448bf1db3a5",
        },
        "6/declaration/body/0/argument/1/3/1/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> BETA",
          },
          hash: "2875edb8f30d3d9d8a85d3784f089859",
        },
        "6/declaration/body/0/argument/1/3/3": {
          content: {
            en: "Verify Your Email",
          },
          hash: "1682e88a6eb7c961ae918fefc7325e2f",
        },
        "6/declaration/body/0/argument/1/3/5": {
          content: {
            en: "We've sent you a verification link to complete your beta signup",
          },
          hash: "de1406c6b29ad743f0aff5cf81c2b9db",
        },
        "6/declaration/body/0/argument/1/5/1/3": {
          content: {
            en: "Check Your Email",
          },
          hash: "128454044f09acb189bc9429173c3fe0",
        },
        "6/declaration/body/0/argument/1/5/1/5": {
          content: {
            en: "We've sent a verification link to your email address",
          },
          hash: "872527bf9544edc5588c39871ec081a8",
        },
        "6/declaration/body/0/argument/1/5/3/1/1/3/1": {
          content: {
            en: "Next Steps:",
          },
          hash: "5c246cbdd0a9294da8c81080e6f9bb44",
        },
        "6/declaration/body/0/argument/1/5/3/1/1/3/3/1": {
          content: {
            en: "1. Check your email inbox",
          },
          hash: "********************************",
        },
        "6/declaration/body/0/argument/1/5/3/1/1/3/3/3": {
          content: {
            en: "2. Click the verification link",
          },
          hash: "17dc06e3a8c38906afe44fbd371941f3",
        },
        "6/declaration/body/0/argument/1/5/3/1/1/3/3/5": {
          content: {
            en: "3. Complete your beta onboarding",
          },
          hash: "65cce9bd73c07e49abd234481e809f87",
        },
        "6/declaration/body/0/argument/1/5/3/3/1": {
          content: {
            en: "Didn't receive the email? Check your spam folder or try signing up again.",
          },
          hash: "ab8fe4647c8831a5b5d5e7d3be5d061d",
        },
        "6/declaration/body/0/argument/1/5/3/3/3/1/1": {
          content: {
            en: "Try Again",
          },
          hash: "04ec3c218b7ac0696eddf54c01bfe67c",
        },
        "6/declaration/body/0/argument/1/5/3/3/3/3/1": {
          content: {
            en: "Already Verified? Sign In<element:ArrowRight></element:ArrowRight>",
          },
          hash: "d41a48963e2082b6de19a984def4405e",
        },
        "6/declaration/body/0/argument/1/7/1": {
          content: {
            en: "← Back to Beta Landing",
          },
          hash: "f21835392835ecbe21183a3c9855d77a",
        },
      },
    },
    "app/beta/dashboard/ai-insights/page.tsx": {
      entries: {
        "5/declaration/body/6/argument/11/1/1": {
          content: {
            en: "<element:Zap></element:Zap> High Priority Insights",
          },
          hash: "7b985570475128560a1d8b7b7f073ca1",
        },
        "5/declaration/body/6/argument/11/1/3": {
          content: {
            en: "These insights require immediate attention",
          },
          hash: "a51af96756128489abefead80b65bc4b",
        },
        "5/declaration/body/6/argument/11/3/1/1/expression/0/body/1/1/3/1/1/3/1":
          {
            content: {
              en: "{insight.priority} priority",
            },
            hash: "85b717f7a0e7b8a432cb58ed481ac234",
          },
        "5/declaration/body/6/argument/11/3/1/1/expression/0/body/1/1/3/1/1/3/5":
          {
            content: {
              en: "{insight.confidence}% confidence",
            },
            hash: "3dab08da1cb703c3a095b14b3a0d254f",
          },
        "5/declaration/body/6/argument/11/3/1/1/expression/0/body/1/1/3/5/expression/right/1":
          {
            content: {
              en: "Take Action",
            },
            hash: "6229b0b33c11e0637ad61cf354094b4e",
          },
        "5/declaration/body/6/argument/11/3/1/1/expression/0/body/1/1/3/5/expression/right/3":
          {
            content: {
              en: "Learn More",
            },
            hash: "03ba35820b8f7cbda9e83b1554970885",
          },
        "5/declaration/body/6/argument/11/3/1/1/expression/0/body/1/1/3/5/expression/right/5":
          {
            content: {
              en: "Dismiss",
            },
            hash: "c0f3a2c928eff83623e7f8d98786fbf8",
          },
        "5/declaration/body/6/argument/15/1/1": {
          content: {
            en: "<element:Brain></element:Brain> All AI Insights",
          },
          hash: "a987ef68af155a334db6a29f221f2320",
        },
        "5/declaration/body/6/argument/15/1/3": {
          content: {
            en: "Complete list of personalized financial recommendations",
          },
          hash: "e7e3ed6d268fbb9bf1ab30ebfc193ab8",
        },
        "5/declaration/body/6/argument/15/3/1/1/expression/0/body/1/1/3/1/1/3/5":
          {
            content: {
              en: "<element:Brain></element:Brain>{insight.confidence}%",
            },
            hash: "ac47abdda4541bf383b8494dffa811c7",
          },
        "5/declaration/body/6/argument/15/3/1/1/expression/0/body/1/1/3/5/expression/right/1":
          {
            content: {
              en: "View Details",
            },
            hash: "0b9480952693794b5961b7507b9cf0a0",
          },
        "5/declaration/body/6/argument/15/3/1/1/expression/0/body/1/1/3/5/expression/right/3":
          {
            content: {
              en: "<element:Eye></element:Eye> Mark as Seen",
            },
            hash: "0c024bd47011714edb347df139c57482",
          },
        "5/declaration/body/6/argument/19/1/1/3/1": {
          content: {
            en: "AI-Powered Financial Intelligence",
          },
          hash: "1c8219ddbcc224ed25babf9dde12e274",
        },
        "5/declaration/body/6/argument/19/1/1/3/3": {
          content: {
            en: "Our machine learning algorithms analyze your spending patterns, financial goals, and market conditions to provide personalized insights that help you make smarter financial decisions.",
          },
          hash: "3a996bdeafb96e90153a476398b0e8ad",
        },
        "5/declaration/body/6/argument/19/1/1/3/5/1/1": {
          content: {
            en: "<element:Brain></element:Brain> Pattern Recognition",
          },
          hash: "bcda1421035000cf337f407acc82e3d2",
        },
        "5/declaration/body/6/argument/19/1/1/3/5/1/3": {
          content: {
            en: "Identifies trends and anomalies in your financial behavior",
          },
          hash: "7c77dff4b2762c8a1215cb5b047308ec",
        },
        "5/declaration/body/6/argument/19/1/1/3/5/3/1": {
          content: {
            en: "<element:Target></element:Target> Goal Optimization",
          },
          hash: "141da23f75be98ba8f402528e957aee2",
        },
        "5/declaration/body/6/argument/19/1/1/3/5/3/3": {
          content: {
            en: "Suggests the most efficient paths to achieve your financial goals",
          },
          hash: "d41220f1e36c4c8dd5a51884d8370007",
        },
        "5/declaration/body/6/argument/19/1/1/3/5/5/1": {
          content: {
            en: "<element:Lightbulb></element:Lightbulb> Proactive Alerts",
          },
          hash: "af0c469495549e59091d542f745c68d2",
        },
        "5/declaration/body/6/argument/19/1/1/3/5/5/3": {
          content: {
            en: "Early warnings about potential financial risks and opportunities",
          },
          hash: "edba6ce1b6c05b0e33525767f0ad2fed",
        },
        "5/declaration/body/6/argument/19/1/1/3/7/1/1": {
          content: {
            en: "Real-time Analysis",
          },
          hash: "7c24297630572dedaaa928bf9830e188",
        },
        "5/declaration/body/6/argument/19/1/1/3/7/1/3": {
          content: {
            en: "Personalized Recommendations",
          },
          hash: "97cc1efbcdececc67698896fca57e4d1",
        },
        "5/declaration/body/6/argument/19/1/1/3/7/1/5": {
          content: {
            en: "Continuous Learning",
          },
          hash: "16e2546c1dc0b191152b531fbf491abc",
        },
        "5/declaration/body/6/argument/19/1/1/3/7/1/7": {
          content: {
            en: "Privacy Protected",
          },
          hash: "538458834afdaa5c1d7c24eb440f5d41",
        },
        "5/declaration/body/6/argument/3/1/1/1": {
          content: {
            en: "AI Insights",
          },
          hash: "feb0c7528a3eca4ae226b362b8c46640",
        },
        "5/declaration/body/6/argument/3/1/1/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> New Beta Feature",
          },
          hash: "ad18f3aee7ff9d2e257c72d797e59fbc",
        },
        "5/declaration/body/6/argument/3/1/3": {
          content: {
            en: "Intelligent financial recommendations powered by machine learning",
          },
          hash: "3db6821c1d66e9b28e83eb1bc49638e8",
        },
        "5/declaration/body/6/argument/3/3/1": {
          content: {
            en: "<element:Settings></element:Settings> Customize",
          },
          hash: "39ffe5a8546b6b856727ac0172437b16",
        },
        "5/declaration/body/6/argument/3/3/3": {
          content: {
            en: "<element:Brain></element:Brain> Refresh Insights",
          },
          hash: "945c411548968d12191b1da45fb07352",
        },
        "5/declaration/body/6/argument/7/1/1/1": {
          content: {
            en: "<element:Brain></element:Brain> Total Insights",
          },
          hash: "b10441d879236e47c191395a406092da",
        },
        "5/declaration/body/6/argument/7/1/3/3": {
          content: {
            en: "Active recommendations",
          },
          hash: "53f0f0b18b3158c972d5e609aeb08eac",
        },
        "5/declaration/body/6/argument/7/3/1/1": {
          content: {
            en: "<element:Zap></element:Zap> High Priority",
          },
          hash: "23b683fc09409242840d209c8a0653e4",
        },
        "5/declaration/body/6/argument/7/3/3/3": {
          content: {
            en: "Require attention",
          },
          hash: "59cf4719ad13b838bc110bc676357be9",
        },
        "5/declaration/body/6/argument/7/5/1/1": {
          content: {
            en: "<element:Target></element:Target> Actionable",
          },
          hash: "f4320985b61d9e710b7c292a7ac05d67",
        },
        "5/declaration/body/6/argument/7/5/3/3": {
          content: {
            en: "Ready to implement",
          },
          hash: "73d2702ced8a718f3c6287886daefbf0",
        },
        "5/declaration/body/6/argument/7/7/1/1": {
          content: {
            en: "<element:DollarSign></element:DollarSign> Potential Savings",
          },
          hash: "6ed58b08477e7c8441607445166b52b6",
        },
        "5/declaration/body/6/argument/7/7/3/1": {
          content: {
            en: "$3,204",
          },
          hash: "85f89bbb4002267ae8f476aaf2b1808b",
        },
        "5/declaration/body/6/argument/7/7/3/3": {
          content: {
            en: "If all applied",
          },
          hash: "fd3c1c591a18d13739ab95f1706d7396",
        },
      },
    },
    "app/beta/dashboard/analytics/page.tsx": {
      entries: {
        "8/declaration/body/8/argument/11/1/1": {
          content: {
            en: "<element:BarChart3></element:BarChart3> Financial Health Score",
          },
          hash: "859518efe385e9715d82a2f1ae02c75f",
        },
        "8/declaration/body/8/argument/11/1/3": {
          content: {
            en: "Overall assessment of your financial wellbeing",
          },
          hash: "0869721a376aef6725ec8ad8fc2bd8a3",
        },
        "8/declaration/body/8/argument/11/3/1/1/1/1": {
          content: {
            en: "{analyticsData.financialHealth.score}/100",
          },
          hash: "e2dbd0e1f5cd7cdbb536f50432438697",
        },
        "8/declaration/body/8/argument/11/3/1/1/1/3": {
          content: {
            en: "Good Financial Health",
          },
          hash: "7bb65644989dfb57ea54b6c53b4ab420",
        },
        "8/declaration/body/8/argument/11/3/1/1/1/5": {
          content: {
            en: "Above average compared to similar households",
          },
          hash: "47575a33e2edb740805ad802531bdbf2",
        },
        "8/declaration/body/8/argument/15/1/1": {
          content: {
            en: "<element:Brain></element:Brain> AI-Powered Financial Insights",
          },
          hash: "ae29ce2addb62ae8768a0ed0b8933b90",
        },
        "8/declaration/body/8/argument/15/1/3": {
          content: {
            en: "Personalized recommendations based on your spending patterns",
          },
          hash: "fde759bcf0953325ebef74343a18e9f3",
        },
        "8/declaration/body/8/argument/15/3/1/1/expression/0/body/1/1/3/5/1": {
          content: {
            en: "{insight.impact} impact",
          },
          hash: "21fdcc1032756074a69f9a5e97b887f1",
        },
        "8/declaration/body/8/argument/15/3/1/1/expression/0/body/1/1/3/5/3/expression/right":
          {
            content: {
              en: "Save <function:formatCurrency/>",
            },
            hash: "8e8cca9c8d4a067a3b13c87606345856",
          },
        "8/declaration/body/8/argument/15/3/1/1/expression/0/body/1/1/3/5/5/expression/right":
          {
            content: {
              en: "+<function:formatCurrency/>",
            },
            hash: "2e7fa01b9eddb96d490a69d7c8e965c6",
          },
        "8/declaration/body/8/argument/15/3/1/1/expression/0/body/1/1/3/5/7/expression/right":
          {
            content: {
              en: "+<function:formatCurrency/>",
            },
            hash: "2e7fa01b9eddb96d490a69d7c8e965c6",
          },
        "8/declaration/body/8/argument/19/1/1": {
          content: {
            en: "<element:PieChart></element:PieChart> Cross-Account Spending Analysis",
          },
          hash: "9f622920d9e4cbd4741e5e6b87492c6f",
        },
        "8/declaration/body/8/argument/19/1/3": {
          content: {
            en: "Personal vs family spending breakdown by category",
          },
          hash: "32a32a92110c77889edbe60c9ee89762",
        },
        "8/declaration/body/8/argument/19/3/1/1/expression/0/body/3/1/1/1": {
          content: {
            en: "Personal",
          },
          hash: "1c619008563115c5c99acdab4e8c9717",
        },
        "8/declaration/body/8/argument/19/3/1/1/expression/0/body/3/3/1/1": {
          content: {
            en: "Family",
          },
          hash: "5328b24179602eb4e554e1758b8c8519",
        },
        "8/declaration/body/8/argument/23/1/1": {
          content: {
            en: "<element:Target></element:Target> Financial Predictions",
          },
          hash: "bc28cab877e83def229d4048a0d30b0d",
        },
        "8/declaration/body/8/argument/23/1/3": {
          content: {
            en: "AI-powered forecasts based on current trends",
          },
          hash: "ccace799a49d642e0b6b90c02cf9f592",
        },
        "8/declaration/body/8/argument/23/3/1/1/expression/0/body/1/1/1/3": {
          content: {
            en: "Forecast in {prediction.timeframe}",
          },
          hash: "40fcbab2753f85447f1b5255efecb16b",
        },
        "8/declaration/body/8/argument/23/3/1/1/expression/0/body/1/1/3/1/1": {
          content: {
            en: "Current",
          },
          hash: "27f172f76ac28e72cb062f80002b0ad5",
        },
        "8/declaration/body/8/argument/23/3/1/1/expression/0/body/1/1/3/3/1": {
          content: {
            en: "Predicted",
          },
          hash: "8e2c52b5758ce4a436f04960416175c1",
        },
        "8/declaration/body/8/argument/23/3/1/1/expression/0/body/1/1/5/1": {
          content: {
            en: "Confidence",
          },
          hash: "69fea4c1281d80dbd96094b1dae8184f",
        },
        "8/declaration/body/8/argument/23/3/1/1/expression/0/body/1/1/5/3/3": {
          content: {
            en: "{prediction.confidence}%",
          },
          hash: "b18b0715d5ed5226537cb0af05a603d2",
        },
        "8/declaration/body/8/argument/27/1/1/3/1": {
          content: {
            en: "Advanced Analytics Features",
          },
          hash: "02b10b47aa7b3f96cafdcda3c795f334",
        },
        "8/declaration/body/8/argument/27/1/1/3/3": {
          content: {
            en: "Unlock deeper insights with machine learning-powered analysis of your financial patterns. Get personalized recommendations, predictive modeling, and optimization suggestions.",
          },
          hash: "506d6c9e390ba6e1ec6d961c279b0e65",
        },
        "8/declaration/body/8/argument/27/1/1/3/5/1/1": {
          content: {
            en: "<element:BarChart3></element:BarChart3> Trend Analysis",
          },
          hash: "49b51e9319e849df8af3ea90a5731a00",
        },
        "8/declaration/body/8/argument/27/1/1/3/5/1/3": {
          content: {
            en: "Multi-dimensional trend detection across all financial data",
          },
          hash: "6df0720867d5734f4fba756f33efa5a6",
        },
        "8/declaration/body/8/argument/27/1/1/3/5/3/1": {
          content: {
            en: "<element:Target></element:Target> Goal Optimization",
          },
          hash: "141da23f75be98ba8f402528e957aee2",
        },
        "8/declaration/body/8/argument/27/1/1/3/5/3/3": {
          content: {
            en: "AI-suggested goal adjustments for optimal achievement rates",
          },
          hash: "7c77927df08ecb6973e7c7168c39b067",
        },
        "8/declaration/body/8/argument/27/1/1/3/5/5/1": {
          content: {
            en: "<element:Lightbulb></element:Lightbulb> Smart Alerts",
          },
          hash: "9f1abdded4bd3298f0c8148d76451126",
        },
        "8/declaration/body/8/argument/27/1/1/3/5/5/3": {
          content: {
            en: "Proactive notifications for financial opportunities and risks",
          },
          hash: "1fc83b3948c70fbc3b42e0c9eb060df0",
        },
        "8/declaration/body/8/argument/27/1/1/3/5/7/1": {
          content: {
            en: "<element:Users></element:Users> Family Insights",
          },
          hash: "01e06a1bda85806e945eb6a1475e0491",
        },
        "8/declaration/body/8/argument/27/1/1/3/5/7/3": {
          content: {
            en: "Collaborative analytics for shared financial decision-making",
          },
          hash: "6c1252bb1c7f641d6055a3e03b1007a1",
        },
        "8/declaration/body/8/argument/3/1/1": {
          content: {
            en: "Advanced Analytics",
          },
          hash: "d456647dfb545040166684f678b0d1e1",
        },
        "8/declaration/body/8/argument/3/1/3": {
          content: {
            en: "AI-powered insights across your complete financial picture",
          },
          hash: "7fbf1920d4c2b6f81b03cd0acaa80d0c",
        },
        "8/declaration/body/8/argument/3/3/1": {
          content: {
            en: "<element:Filter></element:Filter> Customize",
          },
          hash: "39ffe5a8546b6b856727ac0172437b16",
        },
        "8/declaration/body/8/argument/3/3/3": {
          content: {
            en: "<element:Download></element:Download> Export Report",
          },
          hash: "0064e3a1ccbb26b8e12011966672a595",
        },
        "8/declaration/body/8/argument/7/1/1/1/1": {
          content: {
            en: "Analysis Period",
          },
          hash: "3790b1d9de77f656aeb2458e5457bf77",
        },
        "8/declaration/body/8/argument/7/1/1/1/3/1/1-placeholder": {
          content: {
            en: "Select period",
          },
          hash: "f3e492e15b52807eda6248fcdd65e36f",
        },
        "8/declaration/body/8/argument/7/1/1/1/3/3/1": {
          content: {
            en: "Last Month",
          },
          hash: "9ab119dff0cb00d094cef2066572fe92",
        },
        "8/declaration/body/8/argument/7/1/1/1/3/3/3": {
          content: {
            en: "Last 3 Months",
          },
          hash: "9fe9b397bd8f84a4337c93fef22a7325",
        },
        "8/declaration/body/8/argument/7/1/1/1/3/3/5": {
          content: {
            en: "Last 6 Months",
          },
          hash: "40074a0c15af6811bb0688292a2b5e1f",
        },
        "8/declaration/body/8/argument/7/1/1/1/3/3/7": {
          content: {
            en: "Last Year",
          },
          hash: "42547108d452358d115c59f8e663e0a5",
        },
        "8/declaration/body/8/argument/7/1/1/3/1": {
          content: {
            en: "Category Focus",
          },
          hash: "8c01be7c3f65e316f8af72ea60bfa124",
        },
        "8/declaration/body/8/argument/7/1/1/3/3/1/1-placeholder": {
          content: {
            en: "Select category",
          },
          hash: "48c701bd440d494da6a6fac2570e7196",
        },
        "8/declaration/body/8/argument/7/1/1/3/3/3/1": {
          content: {
            en: "All Categories",
          },
          hash: "2547e684d62a4710bdfbb9415a9970d0",
        },
        "8/declaration/body/8/argument/7/1/1/3/3/3/3": {
          content: {
            en: "Housing",
          },
          hash: "a1cfaa568585cef6de46f630d52b1639",
        },
        "8/declaration/body/8/argument/7/1/1/3/3/3/5": {
          content: {
            en: "Food & Dining",
          },
          hash: "d0ec818c9fb648c11760baa6b695feeb",
        },
        "8/declaration/body/8/argument/7/1/1/3/3/3/7": {
          content: {
            en: "Transportation",
          },
          hash: "53db4b5e5eb2967fe70dd233d3b9c770",
        },
        "8/declaration/body/8/argument/7/1/1/3/3/3/9": {
          content: {
            en: "Entertainment",
          },
          hash: "9f0ed7bccb7d8519a96e311fb9f3261b",
        },
        "8/declaration/body/8/argument/7/1/1/5/1": {
          content: {
            en: "<element:Brain></element:Brain> Refresh Analysis",
          },
          hash: "3459e07fb347f2516fe443c7d7b9acd6",
        },
      },
    },
    "app/beta/dashboard/combined/page.tsx": {
      entries: {
        "10/declaration/body/10/argument/11/1/1": {
          content: {
            en: "<element:Layers></element:Layers> Finance Mode",
          },
          hash: "3eb6e0ff887b5b57b82ae1b5973da216",
        },
        "10/declaration/body/10/argument/11/1/3": {
          content: {
            en: "Switch between different views of your financial data with real-time integration",
          },
          hash: "cf979e1e02166463ea3fce3267e077d9",
        },
        "10/declaration/body/10/argument/11/3/1/1/1": {
          content: {
            en: "<element:User></element:User> Personal",
          },
          hash: "71a4a95216e82ef916da83ddcab49ffd",
        },
        "10/declaration/body/10/argument/11/3/1/1/3": {
          content: {
            en: "<element:Users></element:Users> Family",
          },
          hash: "f0baa759c7e84727a38124b0cd57a32d",
        },
        "10/declaration/body/10/argument/11/3/1/1/5": {
          content: {
            en: "<element:Layers></element:Layers> Unified",
          },
          hash: "2f999e9066afa26fd0e3fadc5aceaeaf",
        },
        "10/declaration/body/10/argument/11/3/1/3/1/1/1/1/1/3": {
          content: {
            en: "Personal Mode",
          },
          hash: "c5aea5649c1d05d0e0fa456d53d653ef",
        },
        "10/declaration/body/10/argument/11/3/1/3/1/1/1/1/3": {
          content: {
            en: "Focus on your individual financial accounts, goals, and budgets with real-time data.",
          },
          hash: "38596ec086701271445a097e35562238",
        },
        "10/declaration/body/10/argument/11/3/1/3/1/1/3/1/1/1/1": {
          content: {
            en: "Live Data",
          },
          hash: "f4f221384fd31324b94e820168b16fc8",
        },
        "10/declaration/body/10/argument/11/3/1/3/1/1/3/1/1/1/3": {
          content: {
            en: "Personal Analytics",
          },
          hash: "97ecf4be0f09ea31cb649209dd57621a",
        },
        "10/declaration/body/10/argument/11/3/1/3/1/1/5/1/1/1/1": {
          content: {
            en: "Active",
          },
          hash: "2216451b9fe207e4498e8837545b1c64",
        },
        "10/declaration/body/10/argument/11/3/1/3/1/1/5/1/1/1/3": {
          content: {
            en: "Personal Goals",
          },
          hash: "b419613f1e290e39bb44870e99f61fd0",
        },
        "10/declaration/body/10/argument/11/3/1/3/3/1/1/1/1/3": {
          content: {
            en: "Family Mode",
          },
          hash: "aabaaae77b7e7938471d61376072ac07",
        },
        "10/declaration/body/10/argument/11/3/1/3/3/1/1/1/3": {
          content: {
            en: "View shared accounts, family budgets, and collaborative goals with real-time synchronization.",
          },
          hash: "64d93524818de209502327794c704dfb",
        },
        "10/declaration/body/10/argument/11/3/1/3/3/1/3/1/1/1/1": {
          content: {
            en: "Live Data",
          },
          hash: "f4f221384fd31324b94e820168b16fc8",
        },
        "10/declaration/body/10/argument/11/3/1/3/3/1/3/1/1/1/3": {
          content: {
            en: "Family Analytics",
          },
          hash: "e549f377cab5a3c4954d83eeff318578",
        },
        "10/declaration/body/10/argument/11/3/1/3/3/1/5/1/1/1/1": {
          content: {
            en: "Groups",
          },
          hash: "9335199dd5e8a03e76f15cc1a9a7d7b5",
        },
        "10/declaration/body/10/argument/11/3/1/3/3/1/5/1/1/1/3": {
          content: {
            en: "Family Groups",
          },
          hash: "9a67b9835aeb0444582b784a5291a5d5",
        },
        "10/declaration/body/10/argument/11/3/1/3/5/1/1/1/1/3": {
          content: {
            en: "Unified View",
          },
          hash: "2c268b1d4ee115676bafe8326603e9fb",
        },
        "10/declaration/body/10/argument/11/3/1/3/5/1/1/1/3": {
          content: {
            en: "Complete picture combining personal and family finances with intelligent analytics.",
          },
          hash: "b254ec1ff96fc75125b55a8d3b45ae41",
        },
        "10/declaration/body/10/argument/11/3/1/3/5/1/3/1/1/1/1": {
          content: {
            en: "Live",
          },
          hash: "3c49c2daef66d03532d0c8b3365f77e6",
        },
        "10/declaration/body/10/argument/11/3/1/3/5/1/3/1/1/1/3": {
          content: {
            en: "Total Balance",
          },
          hash: "7144f8fd3508c6c0c1dc285a7b3e19d0",
        },
        "10/declaration/body/10/argument/11/3/1/3/5/1/5/1/1/1/1": {
          content: {
            en: "Real-time",
          },
          hash: "320bdfc8168309215f846b92aeb40d9f",
        },
        "10/declaration/body/10/argument/11/3/1/3/5/1/5/1/1/1/3": {
          content: {
            en: "Net Cash Flow",
          },
          hash: "44f8ccefa06bc8664a49b590cbdc05a7",
        },
        "10/declaration/body/10/argument/11/3/1/3/5/1/7/1/1/1/1": {
          content: {
            en: "Smart",
          },
          hash: "2ff8747fc07b86cc71105712c9b4f131",
        },
        "10/declaration/body/10/argument/11/3/1/3/5/1/7/1/1/1/3": {
          content: {
            en: "Goal Progress",
          },
          hash: "63cdf80e3868c97b4df90c0622c9b8b6",
        },
        "10/declaration/body/10/argument/15/1/1": {
          content: {
            en: "<element:Settings></element:Settings> Power User Features - Now Active",
          },
          hash: "a3b5cc34148b572354553c8c85185e00",
        },
        "10/declaration/body/10/argument/15/1/3": {
          content: {
            en: "Advanced tools for comprehensive financial management with real data",
          },
          hash: "6f8779e6dd25c1d77ccca6c6f6185808",
        },
        "10/declaration/body/10/argument/15/3/1/1/1": {
          content: {
            en: "🔄 Smart Mode Switching",
          },
          hash: "4fc492ea7518dfa0f95ecff5eb775775",
        },
        "10/declaration/body/10/argument/15/3/1/1/3": {
          content: {
            en: "Seamlessly switch between personal, family, and unified views with real-time data synchronization and intelligent context switching.",
          },
          hash: "5ce9fbacebdb7164b9d67d83105b9b24",
        },
        "10/declaration/body/10/argument/15/3/1/1/5": {
          content: {
            en: "📊 Live Analytics Dashboard",
          },
          hash: "00b17b4d7e64e9892dacee46b970f977",
        },
        "10/declaration/body/10/argument/15/3/1/1/7": {
          content: {
            en: "Real-time comprehensive reports that span personal and family finances with actionable insights and trend analysis.",
          },
          hash: "3e762d01e467928c0b2b157bbcb49c86",
        },
        "10/declaration/body/10/argument/15/3/1/3/1": {
          content: {
            en: "🎯 Intelligent Insights",
          },
          hash: "4c1aafaf062eb5b4c20676c8daa78c96",
        },
        "10/declaration/body/10/argument/15/3/1/3/3": {
          content: {
            en: "AI-powered financial insights that analyze your complete financial picture and provide personalized recommendations.",
          },
          hash: "3a0742469536de901c2d14b0a11837fe",
        },
        "10/declaration/body/10/argument/15/3/1/3/5": {
          content: {
            en: "⚡ Real-time Synchronization",
          },
          hash: "4c9929fb9726fd16edbab825700bb469",
        },
        "10/declaration/body/10/argument/15/3/1/3/7": {
          content: {
            en: "Instant updates across all views with real-time notifications and cross-account analysis for better decision-making.",
          },
          hash: "ee759826cf0741378e8b410456a160b2",
        },
        "10/declaration/body/10/argument/3/1/3": {
          content: {
            en: "Complete financial control - switch between personal, family, and unified views",
          },
          hash: "d31dff012b2b25759e30c761e26cd1ea",
        },
        "10/declaration/body/10/argument/3/3/1": {
          content: {
            en: "<element:Sparkles></element:Sparkles> Combined Finance Beta",
          },
          hash: "37eda5cdd1dab55828ee9fe8210bb382",
        },
        "10/declaration/body/10/argument/3/3/3/openingElement/0/value/expression":
          {
            content: {
              en: "<element:MessageCircle></element:MessageCircle> Feedback",
            },
            hash: "9dbaa5dae75fff7a7c16ce4bc054e468",
          },
        "10/declaration/body/10/argument/7/1/1/3/1": {
          content: {
            en: "Welcome to Combined Finance Beta!",
          },
          hash: "55ca3306ec1f297f6f67ed657286c57f",
        },
        "10/declaration/body/10/argument/7/1/1/3/3": {
          content: {
            en: "You now have access to the complete financial management experience. Switch between personal, family, and unified views to get the complete picture of your financial life with real data integration.",
          },
          hash: "bde3c4a863e71aca53b1ed4db0cd19df",
        },
        "10/declaration/body/10/argument/7/1/1/3/5/1": {
          content: {
            en: "Real Data Integration",
          },
          hash: "b8a0679153912bc35d98822a6d84006c",
        },
        "10/declaration/body/10/argument/7/1/1/3/5/3": {
          content: {
            en: "Smart Analytics",
          },
          hash: "75e4ce71d87ebee5b87d00eb8c2dafb4",
        },
        "10/declaration/body/10/argument/7/1/1/3/5/5": {
          content: {
            en: "Cross-Account Insights",
          },
          hash: "398a26660e7aa31ae449256287bc74c1",
        },
      },
    },
    "app/beta/dashboard/family/accounts/page.tsx": {
      entries: {
        "8/declaration/body/10/argument/11/1/3-placeholder": {
          content: {
            en: "Search shared accounts...",
          },
          hash: "f7a793d2bda3bdfd9f8106c20d79da56",
        },
        "8/declaration/body/10/argument/15/1/expression/0/body/1/3/3/1": {
          content: {
            en: "View Details",
          },
          hash: "f89b1a0b178c4e8448cdbee27998ed03",
        },
        "8/declaration/body/10/argument/15/1/expression/0/body/1/3/3/3": {
          content: {
            en: "View Transactions",
          },
          hash: "5f40ba6a896e6e18b910be628ee6e9f2",
        },
        "8/declaration/body/10/argument/15/1/expression/0/body/1/3/3/5": {
          content: {
            en: "Manage Permissions",
          },
          hash: "7f0068ad02c0beff90230dcc78491a85",
        },
        "8/declaration/body/10/argument/15/1/expression/0/body/1/3/3/7": {
          content: {
            en: "Edit Account",
          },
          hash: "3cffb9004fec948e5b6426c8b13c47ea",
        },
        "8/declaration/body/10/argument/15/1/expression/0/body/1/3/3/9": {
          content: {
            en: "Remove from Family",
          },
          hash: "0512a120d61f4cbc3ac585aaa1d1b796",
        },
        "8/declaration/body/10/argument/15/1/expression/0/body/3/1/1/1": {
          content: {
            en: "Current Balance",
          },
          hash: "ca0e3b083dda3eddb819bf20c4b56c17",
        },
        "8/declaration/body/10/argument/15/1/expression/0/body/3/1/3/expression/right/1":
          {
            content: {
              en: "Institution",
            },
            hash: "c1ba2086707a5afd2336c37f875c2536",
          },
        "8/declaration/body/10/argument/15/1/expression/0/body/3/1/5/1": {
          content: {
            en: "Your Permission",
          },
          hash: "fd0de06b04a0d845ace47ec83980157e",
        },
        "8/declaration/body/10/argument/15/1/expression/0/body/3/1/7/1": {
          content: {
            en: "Family Members",
          },
          hash: "7370544f59b6e85b7a437eae70933317",
        },
        "8/declaration/body/10/argument/15/1/expression/0/body/3/1/7/3/3": {
          content: {
            en: "{account.memberCount} members",
          },
          hash: "321c3602634e64d72f72b9629f4ae3e3",
        },
        "8/declaration/body/10/argument/15/1/expression/0/body/3/1/9": {
          content: {
            en: "Last activity: <function:Date.toLocaleDateString/>",
          },
          hash: "cc9a6bf6bbe9be05d1fb97f6e45aba5d",
        },
        "8/declaration/body/10/argument/19/1/1/3/1": {
          content: {
            en: "Account Permissions",
          },
          hash: "8e6ed0e3cfc00e757dd35656971d81c7",
        },
        "8/declaration/body/10/argument/19/1/1/3/3": {
          content: {
            en: "Family accounts have different permission levels to ensure financial security and appropriate access.",
          },
          hash: "aed56b6083f4e2429eda40f59abb509a",
        },
        "8/declaration/body/10/argument/19/1/1/3/5/1/3/1": {
          content: {
            en: "Full Access",
          },
          hash: "6ef966ea1c7aa6acc4153f1209839b44",
        },
        "8/declaration/body/10/argument/19/1/1/3/5/1/3/3": {
          content: {
            en: "View, add, edit transactions",
          },
          hash: "58a0020769604cabf2a213a10a7e0135",
        },
        "8/declaration/body/10/argument/19/1/1/3/5/3/3/1": {
          content: {
            en: "View Only",
          },
          hash: "f4eed829fb53745058299a0aac03e01d",
        },
        "8/declaration/body/10/argument/19/1/1/3/5/3/3/3": {
          content: {
            en: "View balance and transactions",
          },
          hash: "17e19811834c13de6bf4f2fef0d14a46",
        },
        "8/declaration/body/10/argument/19/1/1/3/5/5/3/1": {
          content: {
            en: "Limited",
          },
          hash: "cfe358093922954b7f1c1aeaf3c6f3b8",
        },
        "8/declaration/body/10/argument/19/1/1/3/5/5/3/3": {
          content: {
            en: "View balance only",
          },
          hash: "7dd3340d90e7719266985179d214d6ce",
        },
        "8/declaration/body/10/argument/3/1/1": {
          content: {
            en: "Shared Family Accounts",
          },
          hash: "d241d25b1780fee2eaa757042cc40f96",
        },
        "8/declaration/body/10/argument/3/1/3": {
          content: {
            en: "Manage accounts shared across your family groups",
          },
          hash: "c2fc8a6d8b89bea95d50bb8d5c6875e8",
        },
        "8/declaration/body/10/argument/3/3/1": {
          content: {
            en: "<expression/><expression/> Balances",
          },
          hash: "f3d7f22f98f08375e2484b2170a90b17",
        },
        "8/declaration/body/10/argument/3/3/3": {
          content: {
            en: "<element:Plus></element:Plus> Add Shared Account",
          },
          hash: "d14cabb6bc8c17eb6afd9eb1be003187",
        },
        "8/declaration/body/10/argument/7/1/1": {
          content: {
            en: "<element:Users></element:Users> Family Account Summary",
          },
          hash: "a9d070e07df4acd887833fbe8ad5d03d",
        },
        "8/declaration/body/10/argument/7/3/1/1/1": {
          content: {
            en: "Total Family Balance",
          },
          hash: "6d1e908432fa3c2bdb196105521db3f2",
        },
        "8/declaration/body/10/argument/7/3/1/3/1": {
          content: {
            en: "Shared Accounts",
          },
          hash: "2c50e0af8f6b80a53d0532e6aacf40cb",
        },
        "8/declaration/body/10/argument/7/3/1/5/1": {
          content: {
            en: "Family Members",
          },
          hash: "7370544f59b6e85b7a437eae70933317",
        },
        "8/declaration/body/10/argument/7/3/1/5/3": {
          content: {
            en: "4",
          },
          hash: "ffacf6641976ed5ef76edd45fba94413",
        },
      },
    },
    "app/beta/dashboard/family/budgets/page.tsx": {
      entries: {
        "7/declaration/body/8/argument/11/1/1": {
          content: {
            en: "<element:PieChart></element:PieChart> Family Budget Overview",
          },
          hash: "97f2aac8ec916ca09529d4087243ff49",
        },
        "7/declaration/body/8/argument/11/1/3": {
          content: {
            en: "Combined spending across all family budgets",
          },
          hash: "13aab5d101f2b68db8d31add7a327545",
        },
        "7/declaration/body/8/argument/11/3/1/1/1/1": {
          content: {
            en: "Overall Progress",
          },
          hash: "a694022ee22cf5f53a4d411264a2f225",
        },
        "7/declaration/body/8/argument/11/3/1/1/1/3": {
          content: {
            en: "<function:formatCurrency/> / <function:formatCurrency/>",
          },
          hash: "776d6623bafb3946d883156d6d0fe06e",
        },
        "7/declaration/body/8/argument/11/3/1/1/5": {
          content: {
            en: "<function:toFixed/> % remaining this period",
          },
          hash: "39aa91afa7f31fa17b089f33fb007e5d",
        },
        "7/declaration/body/8/argument/15/1/expression/0/body/3/1/11/1": {
          content: {
            en: "View Details",
          },
          hash: "00600fcd9b56016cd81ea8c149e0defd",
        },
        "7/declaration/body/8/argument/15/1/expression/0/body/3/1/11/3": {
          content: {
            en: "Add Expense",
          },
          hash: "ae310e7b38057d7a04c1648cbaa60286",
        },
        "7/declaration/body/8/argument/15/1/expression/0/body/3/1/13": {
          content: {
            en: "Last updated: <function:Date.toLocaleDateString/>",
          },
          hash: "002832a11dc5be658de64d27fff7b8b9",
        },
        "7/declaration/body/8/argument/15/1/expression/0/body/3/1/3/1/1": {
          content: {
            en: "Progress",
          },
          hash: "dd0200d5849ebb7d64c15098ae91d229",
        },
        "7/declaration/body/8/argument/15/1/expression/0/body/3/1/3/1/3": {
          content: {
            en: "<function:formatCurrency/> / <function:formatCurrency/>",
          },
          hash: "6dec189d92f20ef6bcfca752b6d0c04a",
        },
        "7/declaration/body/8/argument/15/1/expression/0/body/3/1/3/5": {
          content: {
            en: "<function:getProgressPercentage/> % used",
          },
          hash: "2f2e5d3b1cb2c1d3db2498b637dd0d41",
        },
        "7/declaration/body/8/argument/15/1/expression/0/body/3/1/7/1/1": {
          content: {
            en: "Contributors",
          },
          hash: "8434bdfc76b7926925469e025b878919",
        },
        "7/declaration/body/8/argument/15/1/expression/0/body/3/1/7/1/3": {
          content: {
            en: "{budget.contributors.length} members",
          },
          hash: "548029f0eabb252fdbd2c0435ec17ca2",
        },
        "7/declaration/body/8/argument/15/1/expression/0/body/3/1/7/3/3/expression/right":
          {
            content: {
              en: "+<expression/>",
            },
            hash: "98e5e138907894abfc27d1be89d002d4",
          },
        "7/declaration/body/8/argument/19/1/1/3/1": {
          content: {
            en: "Family Budget Collaboration",
          },
          hash: "54f789fff2d7af44543338500e11cbe8",
        },
        "7/declaration/body/8/argument/19/1/1/3/3": {
          content: {
            en: "Family budgets allow multiple members to contribute expenses and track progress together. Set spending limits, assign categories, and receive notifications when budgets need attention.",
          },
          hash: "2433380a334ced3ab457b110e41a922a",
        },
        "7/declaration/body/8/argument/19/1/1/3/5/1": {
          content: {
            en: "Real-time Updates",
          },
          hash: "3b07e4d60da488cdfadf8c94647e4d24",
        },
        "7/declaration/body/8/argument/19/1/1/3/5/3": {
          content: {
            en: "Expense Notifications",
          },
          hash: "f46abe24af86847f04e6677ae6094250",
        },
        "7/declaration/body/8/argument/19/1/1/3/5/5": {
          content: {
            en: "Category Tracking",
          },
          hash: "7b52fb0fe1f10aac2d8abb49dffdfe3c",
        },
        "7/declaration/body/8/argument/19/1/1/3/5/7": {
          content: {
            en: "Member Permissions",
          },
          hash: "77ddce94d0c830b5889a84f5ccc2546e",
        },
        "7/declaration/body/8/argument/3/1/1": {
          content: {
            en: "Family Budgets",
          },
          hash: "f9ceb1c4f162ee704f4142efa87e18c9",
        },
        "7/declaration/body/8/argument/3/1/3": {
          content: {
            en: "Collaborative budget management for your family",
          },
          hash: "24c2f9ce8d7479d57b01d92b658632e1",
        },
        "7/declaration/body/8/argument/3/3/1": {
          content: {
            en: "<element:Users></element:Users> Manage Contributors",
          },
          hash: "beaff5895f132beeb9d10e45d89e9f83",
        },
        "7/declaration/body/8/argument/3/3/3": {
          content: {
            en: "<element:Plus></element:Plus> Create Family Budget",
          },
          hash: "fabdb1409dcd747aad27a3ec8a2f52ba",
        },
        "7/declaration/body/8/argument/7/1/1/1": {
          content: {
            en: "<element:DollarSign></element:DollarSign> Total Budgeted",
          },
          hash: "ee86ade3314cc7cde27be71ddb1368d3",
        },
        "7/declaration/body/8/argument/7/3/1/1": {
          content: {
            en: "<element:TrendingUp></element:TrendingUp> Total Spent",
          },
          hash: "5702b66c98c271e8651fb2e97621cbed",
        },
        "7/declaration/body/8/argument/7/5/1/1": {
          content: {
            en: "<element:PieChart></element:PieChart> Active Budgets",
          },
          hash: "54344f35caaa92252cc6c584a0b66a47",
        },
        "7/declaration/body/8/argument/7/7/1/1": {
          content: {
            en: "<element:Users></element:Users> Contributors",
          },
          hash: "63413c401205e89883a4cc364619e8f2",
        },
        "7/declaration/body/8/argument/7/7/3/1": {
          content: {
            en: "4",
          },
          hash: "ffacf6641976ed5ef76edd45fba94413",
        },
      },
    },
    "app/beta/dashboard/family/goals/page.tsx": {
      entries: {
        "7/declaration/body/9/argument/11/1/1": {
          content: {
            en: "<element:Target></element:Target> Family Goals Progress",
          },
          hash: "fc0fc9d058fb7b776ce1075d9395a884",
        },
        "7/declaration/body/9/argument/11/1/3": {
          content: {
            en: "Combined progress across all family financial goals",
          },
          hash: "52e5868d524b488a09d64288b1ed2e45",
        },
        "7/declaration/body/9/argument/11/3/1/1/1/1": {
          content: {
            en: "Overall Progress",
          },
          hash: "a694022ee22cf5f53a4d411264a2f225",
        },
        "7/declaration/body/9/argument/11/3/1/1/1/3": {
          content: {
            en: "<function:formatCurrency/> / <function:formatCurrency/>",
          },
          hash: "e46e98b1acca673b26cec366f1351610",
        },
        "7/declaration/body/9/argument/11/3/1/1/5": {
          content: {
            en: "<function:getProgressPercentage/> % of total family goals achieved",
          },
          hash: "472f0784bf1f3d89428b6d613cf00445",
        },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/11/1/1": {
          content: {
            en: "Monthly Target",
          },
          hash: "ed95955a2d98bbcf59e71bf0270e3eda",
        },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/15/1/1": {
          content: {
            en: "Contributors",
          },
          hash: "8434bdfc76b7926925469e025b878919",
        },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/15/1/3": {
          content: {
            en: "{goal.contributors.length} members",
          },
          hash: "548029f0eabb252fdbd2c0435ec17ca2",
        },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/15/3/3/expression/right":
          {
            content: {
              en: "+<expression/>",
            },
            hash: "98e5e138907894abfc27d1be89d002d4",
          },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/23/1": {
          content: {
            en: "Contribute",
          },
          hash: "ffe3933cdc089e6e632ae8aa4bb9140e",
        },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/23/3": {
          content: {
            en: "View Details",
          },
          hash: "00600fcd9b56016cd81ea8c149e0defd",
        },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/3/1/1": {
          content: {
            en: "Progress",
          },
          hash: "dd0200d5849ebb7d64c15098ae91d229",
        },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/3/1/3": {
          content: {
            en: "<function:formatCurrency/> / <function:formatCurrency/>",
          },
          hash: "6dec189d92f20ef6bcfca752b6d0c04a",
        },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/3/5": {
          content: {
            en: "<function:getProgressPercentage/> % complete",
          },
          hash: "1219e60d063f68587c3237903d8d1d88",
        },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/7/1/1": {
          content: {
            en: "Target Date",
          },
          hash: "3af0a7ef7e53c5e6bbf99f9611a9f47f",
        },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/7/3/1": {
          content: {
            en: "<function:getDaysRemaining/> days",
          },
          hash: "9a18540abbbc87d2a22a5ea7fe2031c0",
        },
        "7/declaration/body/9/argument/15/1/expression/0/body/3/1/7/3/3": {
          content: {
            en: "remaining",
          },
          hash: "********************************",
        },
        "7/declaration/body/9/argument/19/1/1/3/1": {
          content: {
            en: "Family Goal Success",
          },
          hash: "ed1a9ce49e556787dd01a40fadc554ca",
        },
        "7/declaration/body/9/argument/19/1/1/3/3": {
          content: {
            en: "Achieve your dreams faster by working together as a family. Set shared goals, track progress in real-time, and celebrate milestones together. Every contribution brings your family closer to financial success.",
          },
          hash: "497e69c2ef8663f96951330801765e2c",
        },
        "7/declaration/body/9/argument/19/1/1/3/5/1": {
          content: {
            en: "Milestone Tracking",
          },
          hash: "5bcc7f2d92b48fab07459190734b7a66",
        },
        "7/declaration/body/9/argument/19/1/1/3/5/3": {
          content: {
            en: "Progress Notifications",
          },
          hash: "a1156ff938bb08a024c7594086b18dd0",
        },
        "7/declaration/body/9/argument/19/1/1/3/5/5": {
          content: {
            en: "Shared Contributions",
          },
          hash: "24fc0552fcf44e1994674ab977059472",
        },
        "7/declaration/body/9/argument/19/1/1/3/5/7": {
          content: {
            en: "Goal Insights",
          },
          hash: "32211f11a328673ba8d861ba408b6f82",
        },
        "7/declaration/body/9/argument/3/1/1": {
          content: {
            en: "Family Goals",
          },
          hash: "c8bfd0270baafb021794081a90ffe71a",
        },
        "7/declaration/body/9/argument/3/1/3": {
          content: {
            en: "Achieve your family's financial dreams together",
          },
          hash: "0071cfbdb7458cd25e941fdf5493a0b1",
        },
        "7/declaration/body/9/argument/3/3/1": {
          content: {
            en: "<element:Users></element:Users> Manage Contributors",
          },
          hash: "beaff5895f132beeb9d10e45d89e9f83",
        },
        "7/declaration/body/9/argument/3/3/3": {
          content: {
            en: "<element:Plus></element:Plus> Create Family Goal",
          },
          hash: "291c15623622fa9c07ef28ea215dc3e0",
        },
        "7/declaration/body/9/argument/7/1/1/1": {
          content: {
            en: "<element:Target></element:Target> Total Goal Value",
          },
          hash: "4d62b0f986ee93daf11544b4d68545cb",
        },
        "7/declaration/body/9/argument/7/3/1/1": {
          content: {
            en: "<element:TrendingUp></element:TrendingUp> Amount Saved",
          },
          hash: "288b83fc2562fd9fcdb39f0970f152eb",
        },
        "7/declaration/body/9/argument/7/5/1/1": {
          content: {
            en: "<element:Calendar></element:Calendar> Active Goals",
          },
          hash: "79a0c3b28ebba264b23e7d2cdfb39376",
        },
        "7/declaration/body/9/argument/7/7/1/1": {
          content: {
            en: "<element:Users></element:Users> Contributors",
          },
          hash: "63413c401205e89883a4cc364619e8f2",
        },
        "7/declaration/body/9/argument/7/7/3/1": {
          content: {
            en: "5",
          },
          hash: "1ca5010e2cefa72820ba8a61ce60b74f",
        },
      },
    },
    "app/beta/dashboard/family/groups/page.tsx": {
      entries: {
        "7/declaration/body/3/argument/11/1/1": {
          content: {
            en: "<element:Users></element:Users> Your Family Groups",
          },
          hash: "da54f20ab795efc2c0a70d03f41e77be",
        },
        "7/declaration/body/3/argument/11/1/3": {
          content: {
            en: "Groups you're currently part of",
          },
          hash: "9c7041a3f619fbecf000fee569769d3a",
        },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/1/3/3/1":
          {
            content: {
              en: "{group.memberCount} members",
            },
            hash: "321c3602634e64d72f72b9629f4ae3e3",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/1/3/3/3":
          {
            content: {
              en: "•",
            },
            hash: "5f1c90d0fd46023ba1bfaaa2537f59fb",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/1/3/3/5":
          {
            content: {
              en: "Joined <function:Date.toLocaleDateString/>",
            },
            hash: "93db8b8b84e3a3a23a0ddf37100dcd46",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/1/3/3/7":
          {
            content: {
              en: "•",
            },
            hash: "5f1c90d0fd46023ba1bfaaa2537f59fb",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/1/3/3/9/1/expression/alternate":
          {
            content: {
              en: "<element:Shield></element:Shield> Member",
            },
            hash: "3118de4b66975e7f207f466ef3eb183b",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/1/3/3/9/1/expression/consequent":
          {
            content: {
              en: "<element:Crown></element:Crown> Admin",
            },
            hash: "68128f076fedb2464daddde8dcccf08c",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/3/1/3": {
          content: {
            en: "Group Balance",
          },
          hash: "3e7cbbcf45d1a4d545d1d7f5cb40ca6b",
        },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/3/3/3/1":
          {
            content: {
              en: "View Dashboard",
            },
            hash: "56240ef5ee0639ece21ebbd3f5349537",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/3/3/3/3":
          {
            content: {
              en: "Manage Members",
            },
            hash: "b7998922dfd445bacd5ef8552c12a3eb",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/3/3/3/5":
          {
            content: {
              en: "Group Settings",
            },
            hash: "011537462d83582bff421822f3333d88",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/3/3/3/7/expression/right/1":
          {
            content: {
              en: "Invite Members",
            },
            hash: "d64fe75c8cd158f1186f48f708e4bb92",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/3/3/3/7/expression/right/3":
          {
            content: {
              en: "Delete Group",
            },
            hash: "bba60c069254521c5a7cb9620d843d16",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/1/3/3/3/9/expression/right":
          {
            content: {
              en: "Leave Group",
            },
            hash: "ca121f69f8d622de31aafe6f06131c92",
          },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/3/1": {
          content: {
            en: "Dashboard",
          },
          hash: "d881c52a265f0984c8e56f871398ad15",
        },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/3/3": {
          content: {
            en: "Accounts",
          },
          hash: "d98fea9f2ef7be14985fae780c4b5486",
        },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/3/5": {
          content: {
            en: "Budgets",
          },
          hash: "77f52afc20b914195fd18ecf8d5286c5",
        },
        "7/declaration/body/3/argument/11/3/1/1/expression/0/body/1/3/7": {
          content: {
            en: "Reports",
          },
          hash: "b33c53a840773ffd88254804d99ee154",
        },
        "7/declaration/body/3/argument/15/1/1/3/1": {
          content: {
            en: "Family Group Security",
          },
          hash: "bdfe95f2632c1f48224ec0a7b866f591",
        },
        "7/declaration/body/3/argument/15/1/1/3/3": {
          content: {
            en: "Family groups use role-based permissions to ensure financial data is shared safely. Admins can manage members and settings, while members can view and contribute to shared financial goals and budgets.",
          },
          hash: "1c413e157f0dfbee350d23e845896756",
        },
        "7/declaration/body/3/argument/15/1/1/3/5/1": {
          content: {
            en: "Bank-level Security",
          },
          hash: "ee59a8ed028ba038bdf0875174f63748",
        },
        "7/declaration/body/3/argument/15/1/1/3/5/3": {
          content: {
            en: "Role-based Access",
          },
          hash: "f9b4b54056ca73de3f624e5fe6293a83",
        },
        "7/declaration/body/3/argument/15/1/1/3/5/5": {
          content: {
            en: "Audit Logs",
          },
          hash: "27d54b46ce98de4e991de1bd128cf06a",
        },
        "7/declaration/body/3/argument/3/1/1": {
          content: {
            en: "Family Groups",
          },
          hash: "62d05d1280a673b5f830b6d8739e2b46",
        },
        "7/declaration/body/3/argument/3/1/3": {
          content: {
            en: "Manage your family financial collaborations",
          },
          hash: "28b5a0e9b25868a409ca928e593c9969",
        },
        "7/declaration/body/3/argument/3/3/1": {
          content: {
            en: "<element:Mail></element:Mail> Invitations",
          },
          hash: "ae896d95b3ca440203ba09437f247ace",
        },
        "7/declaration/body/3/argument/3/3/3": {
          content: {
            en: "<element:Plus></element:Plus> Create Group",
          },
          hash: "e80d389d9a6826ca9faa0c99d8f76580",
        },
        "7/declaration/body/3/argument/7/1/1/1": {
          content: {
            en: "<element:Plus></element:Plus> Create New Group",
          },
          hash: "8d7a87188964c8127caf1eaf8677182a",
        },
        "7/declaration/body/3/argument/7/1/3/1": {
          content: {
            en: "Start a new family financial group and invite members to collaborate.",
          },
          hash: "b31c01d9d98693d273f1a88c16e08bb3",
        },
        "7/declaration/body/3/argument/7/1/3/3": {
          content: {
            en: "Get Started",
          },
          hash: "1d5f030c4ec9c869e647ae060518b948",
        },
        "7/declaration/body/3/argument/7/3/1/1": {
          content: {
            en: "<element:UserPlus></element:UserPlus> Join Existing Group",
          },
          hash: "c3ed263585b1680e02391495f44ed245",
        },
        "7/declaration/body/3/argument/7/3/3/1": {
          content: {
            en: "Join a family group using an invitation code or link.",
          },
          hash: "12c3040af91d51fc55d8f9af5fc2994b",
        },
        "7/declaration/body/3/argument/7/3/3/3": {
          content: {
            en: "Enter Code",
          },
          hash: "c9a739fd057f649d6ac2c3c0799b78bb",
        },
        "7/declaration/body/3/argument/7/5/1/1": {
          content: {
            en: "<element:Mail></element:Mail> Pending Invitations",
          },
          hash: "a4608d751dd42196f14f7ca041e988dd",
        },
        "7/declaration/body/3/argument/7/5/3/1": {
          content: {
            en: "View and manage your pending group invitations.",
          },
          hash: "c23f475cf2fefc8acc7d27f7125eaa98",
        },
        "7/declaration/body/3/argument/7/5/3/3": {
          content: {
            en: "View Invitations<element:Badge>2</element:Badge>",
          },
          hash: "7f4926cc26b7951eb538772a4f2f0b50",
        },
      },
    },
    "app/beta/dashboard/family/page.tsx": {
      entries: {
        "7/declaration/body/2/argument/11/1/1/1": {
          content: {
            en: "<element:Plus></element:Plus> Create Family Group",
          },
          hash: "162c126e4f6a851090eb4ea706b93a9b",
        },
        "7/declaration/body/2/argument/11/1/3/1": {
          content: {
            en: "Start your family's financial journey together. Create a group to share budgets, goals, and accounts.",
          },
          hash: "ea942df7bd3a1be5253d429a177230df",
        },
        "7/declaration/body/2/argument/11/1/3/3": {
          content: {
            en: "<element:Users></element:Users> Create Group",
          },
          hash: "d7a7a4f859d5ef1aa07b4ce88a727e5a",
        },
        "7/declaration/body/2/argument/11/3/1/1": {
          content: {
            en: "<element:UserPlus></element:UserPlus> Join Family Group",
          },
          hash: "a2d7a09c507585ab3166b9b79dc1a22d",
        },
        "7/declaration/body/2/argument/11/3/3/1": {
          content: {
            en: "Join an existing family group using an invitation link or code from a family member.",
          },
          hash: "66dcbf9b4f278f4318fc6d6c8d5f2592",
        },
        "7/declaration/body/2/argument/11/3/3/3": {
          content: {
            en: "<element:Heart></element:Heart> Join Group",
          },
          hash: "36d6cf940c273c4e0dd6b12d71e5b3f8",
        },
        "7/declaration/body/2/argument/11/5/1/1": {
          content: {
            en: "<element:Shield></element:Shield> Family Security",
          },
          hash: "2cc0854a7c81edfbf3d8942765af7358",
        },
        "7/declaration/body/2/argument/11/5/3/1": {
          content: {
            en: "Learn about privacy settings, permissions, and how we keep your family's financial data secure.",
          },
          hash: "9feb79fd74b097d0ad70fd334decc508",
        },
        "7/declaration/body/2/argument/11/5/3/3": {
          content: {
            en: "<element:Shield></element:Shield> Security Guide",
          },
          hash: "1d5b1e31184fc234d0fa6185ab0d5d16",
        },
        "7/declaration/body/2/argument/15/1/1/1": {
          content: {
            en: "<element:Heart></element:Heart> Family Collaboration Features",
          },
          hash: "ae5eff1771a8cef6f6ae8c406fce2763",
        },
        "7/declaration/body/2/argument/15/1/1/3": {
          content: {
            en: "What makes family finance special",
          },
          hash: "cee06dbeac0bed056fdae3b07deae9c2",
        },
        "7/declaration/body/2/argument/15/1/3/1/1/3/1": {
          content: {
            en: "Shared Budgets",
          },
          hash: "6be1129d5533d76066b0b8006312c4d1",
        },
        "7/declaration/body/2/argument/15/1/3/1/1/3/3": {
          content: {
            en: "Create budgets that everyone can contribute to and track together in real-time.",
          },
          hash: "017b67b56a6796d758deacfbf5eca61f",
        },
        "7/declaration/body/2/argument/15/1/3/1/3/3/1": {
          content: {
            en: "Family Goals",
          },
          hash: "c8bfd0270baafb021794081a90ffe71a",
        },
        "7/declaration/body/2/argument/15/1/3/1/3/3/3": {
          content: {
            en: "Set shared financial goals like vacation funds, emergency savings, or home improvements.",
          },
          hash: "65cb3f6c66e762d3d3166f932e66767a",
        },
        "7/declaration/body/2/argument/15/1/3/1/5/3/1": {
          content: {
            en: "Shared Accounts",
          },
          hash: "ac2b1e3ee7fcd36f30b04ef6e478ee89",
        },
        "7/declaration/body/2/argument/15/1/3/1/5/3/3": {
          content: {
            en: "Manage joint accounts and track family expenses with proper permissions and visibility.",
          },
          hash: "f5a49a46107e1b0186d24f6596f8b06e",
        },
        "7/declaration/body/2/argument/15/3/1/1": {
          content: {
            en: "<element:TrendingUp></element:TrendingUp> Getting Started Guide",
          },
          hash: "3bb6207d8a091464f44fd30b2dc1a6e9",
        },
        "7/declaration/body/2/argument/15/3/1/3": {
          content: {
            en: "Your path to family financial harmony",
          },
          hash: "4a580793eefb30193a6ab87eee2cdf1b",
        },
        "7/declaration/body/2/argument/15/3/3/1/1/1/1/1": {
          content: {
            en: "1",
          },
          hash: "1505ba253db621b09a196cda5922c87e",
        },
        "7/declaration/body/2/argument/15/3/3/1/1/1/3/1": {
          content: {
            en: "Create or Join Group",
          },
          hash: "f686eccbc725ba3afddcce61b6e6d846",
        },
        "7/declaration/body/2/argument/15/3/3/1/1/1/3/3": {
          content: {
            en: "Start with family group setup",
          },
          hash: "25055f27e58bf8a4dcdc45faffe5f576",
        },
        "7/declaration/body/2/argument/15/3/3/1/1/3": {
          content: {
            en: "Start",
          },
          hash: "e261a2a5c3c980e491f0960cf7b8d79b",
        },
        "7/declaration/body/2/argument/15/3/3/1/3/1/1/1": {
          content: {
            en: "2",
          },
          hash: "6d08a041667d2f923756508f513eba2a",
        },
        "7/declaration/body/2/argument/15/3/3/1/3/1/3/1": {
          content: {
            en: "Add Shared Accounts",
          },
          hash: "d1648271587c4fd88de0895e1591a2d9",
        },
        "7/declaration/body/2/argument/15/3/3/1/3/1/3/3": {
          content: {
            en: "Connect your family's accounts",
          },
          hash: "d925d9ece96d02edf847ca50ef1e7f01",
        },
        "7/declaration/body/2/argument/15/3/3/1/3/3": {
          content: {
            en: "Next",
          },
          hash: "ea5b8bda4a0bfa2f569451059117a856",
        },
        "7/declaration/body/2/argument/15/3/3/1/5/1/1/1": {
          content: {
            en: "3",
          },
          hash: "b7ce945c2f2f0cb6d1586a779f250f66",
        },
        "7/declaration/body/2/argument/15/3/3/1/5/1/3/1": {
          content: {
            en: "Set Family Goals",
          },
          hash: "c9fd55199296e9f468bdc52e3837a9c2",
        },
        "7/declaration/body/2/argument/15/3/3/1/5/1/3/3": {
          content: {
            en: "Define shared objectives",
          },
          hash: "9f00b9d034483ab9d6f4178013c47d42",
        },
        "7/declaration/body/2/argument/15/3/3/1/5/3": {
          content: {
            en: "Later",
          },
          hash: "a12b3ebc8642380a1cd71d09016c4389",
        },
        "7/declaration/body/2/argument/19/1/3": {
          content: {
            en: "Family Dashboard Coming Soon",
          },
          hash: "4d5fb0b382a8d2ef55cc692ca8607b92",
        },
        "7/declaration/body/2/argument/19/1/5": {
          content: {
            en: "We're actively developing the full family finance experience. This includes family group management, shared budgets, collaborative goal setting, and real-time activity feeds. Your feedback helps us prioritize which features to build first!",
          },
          hash: "2af58bfdfaf3d3acbd739e73949abc50",
        },
        "7/declaration/body/2/argument/19/1/7/1": {
          content: {
            en: "<element:ArrowRight></element:ArrowRight> Try Personal Finance",
          },
          hash: "11dd09947613fb02f95af19fb35c9f7d",
        },
        "7/declaration/body/2/argument/19/1/7/3/openingElement/0/value/expression":
          {
            content: {
              en: "<element:MessageCircle></element:MessageCircle> Share Ideas",
            },
            hash: "69b0ea62c87fb9b7694f173cb716d362",
          },
        "7/declaration/body/2/argument/23/1/1": {
          content: {
            en: "<element:Sparkles></element:Sparkles> Why Choose Family Finance?",
          },
          hash: "11c4399447880ea5a578ad7c646b2f0f",
        },
        "7/declaration/body/2/argument/23/1/3": {
          content: {
            en: "Benefits of collaborative financial management",
          },
          hash: "b2cb753c943439464e9df2ce494bb73a",
        },
        "7/declaration/body/2/argument/23/3/1/1/1": {
          content: {
            en: "🎯 Better Financial Alignment",
          },
          hash: "7d15a1d34a91343bc65d7176619b4cb4",
        },
        "7/declaration/body/2/argument/23/3/1/1/3": {
          content: {
            en: "When everyone can see the family's financial picture, it's easier to make decisions that support shared goals and priorities.",
          },
          hash: "abeaabb5749a5853e86429e69d5822af",
        },
        "7/declaration/body/2/argument/23/3/1/1/5": {
          content: {
            en: "💬 Improved Communication",
          },
          hash: "c0001fb13eaf822b514f000fec32ff00",
        },
        "7/declaration/body/2/argument/23/3/1/1/7": {
          content: {
            en: "Reduce money-related stress with transparent spending tracking and collaborative budget planning that keeps everyone informed.",
          },
          hash: "423b5b9e5a7938b7b0cc369ac1e82f26",
        },
        "7/declaration/body/2/argument/23/3/1/3/1": {
          content: {
            en: "🚀 Faster Goal Achievement",
          },
          hash: "a5b02d1f8e767c3d892c8fd471770711",
        },
        "7/declaration/body/2/argument/23/3/1/3/3": {
          content: {
            en: "Families using collaborative financial tools reach their savings goals 40% faster than those managing finances individually.",
          },
          hash: "5790585fd918c57b40605fb819644d1c",
        },
        "7/declaration/body/2/argument/23/3/1/3/5": {
          content: {
            en: "🛡️ Enhanced Security",
          },
          hash: "10a94339b4daac9355ad6322dfa21d54",
        },
        "7/declaration/body/2/argument/23/3/1/3/7": {
          content: {
            en: "Role-based permissions ensure everyone has appropriate access while maintaining privacy and security for sensitive financial information.",
          },
          hash: "2273143755e5631e28e0cb796afd7169",
        },
        "7/declaration/body/2/argument/3/1/3": {
          content: {
            en: "Welcome to Family Finance - designed for collaboration and shared financial goals",
          },
          hash: "6f617033a9af68a84707d112217d0693",
        },
        "7/declaration/body/2/argument/3/3/1": {
          content: {
            en: "<element:Sparkles></element:Sparkles> Family Finance Beta",
          },
          hash: "390f6c38be1bbad8be400917edeab569",
        },
        "7/declaration/body/2/argument/3/3/3/openingElement/0/value/expression":
          {
            content: {
              en: "<element:MessageCircle></element:MessageCircle> Feedback",
            },
            hash: "9dbaa5dae75fff7a7c16ce4bc054e468",
          },
        "7/declaration/body/2/argument/7/1/1/3/1": {
          content: {
            en: "Welcome to Family Finance Beta!",
          },
          hash: "3961369e3220b2f181b1793a96af9457",
        },
        "7/declaration/body/2/argument/7/1/1/3/3": {
          content: {
            en: "You're experiencing a collaboration-first financial management interface designed specifically for families. Share budgets, track goals together, and build financial harmony.",
          },
          hash: "26c8182021b8ddb7fbb34c789e24accc",
        },
        "7/declaration/body/2/argument/7/1/1/3/5/1": {
          content: {
            en: "Real-time Collaboration",
          },
          hash: "b5f327eec22cea4f5ff1c6c2a5d900bc",
        },
        "7/declaration/body/2/argument/7/1/1/3/5/3": {
          content: {
            en: "Shared Goals",
          },
          hash: "********************************",
        },
        "7/declaration/body/2/argument/7/1/1/3/5/5": {
          content: {
            en: "Family Insights",
          },
          hash: "dd54d8bcb4822cc8ffa5047cb9c7031b",
        },
      },
    },
    "app/beta/dashboard/family/reports/page.tsx": {
      entries: {
        "7/declaration/body/8/argument/11/1/1/1": {
          content: {
            en: "Total Income",
          },
          hash: "19059a0172e26244373801ff902d993b",
        },
        "7/declaration/body/8/argument/11/11/1/1": {
          content: {
            en: "Savings Rate",
          },
          hash: "7ec6c5994b66f69ca909f119500ee635",
        },
        "7/declaration/body/8/argument/11/11/3/1/3": {
          content: {
            en: "{mockMetrics.savingsRate}%",
          },
          hash: "5f3f9899e166504f3e889fdec536c966",
        },
        "7/declaration/body/8/argument/11/3/1/1": {
          content: {
            en: "Total Expenses",
          },
          hash: "57bfc5bd4e97342446e0423f6a9c0fd0",
        },
        "7/declaration/body/8/argument/11/5/1/1": {
          content: {
            en: "Net Cash Flow",
          },
          hash: "e282bdbbdab6c583ad45bb9d328dfe5b",
        },
        "7/declaration/body/8/argument/11/7/1/1": {
          content: {
            en: "Budget Variance",
          },
          hash: "fc68c389d02b6efeaa20b3ccdae3324a",
        },
        "7/declaration/body/8/argument/11/9/1/1": {
          content: {
            en: "Goals Progress",
          },
          hash: "de83ad301aaa92ea748bf87fdf0537cd",
        },
        "7/declaration/body/8/argument/11/9/3/1/3": {
          content: {
            en: "{mockMetrics.goalsProgress}%",
          },
          hash: "5f3f9899e166504f3e889fdec536c966",
        },
        "7/declaration/body/8/argument/15/1/1": {
          content: {
            en: "<element:BarChart3></element:BarChart3> Available Family Reports",
          },
          hash: "95ea88cdb5f2e9f2069ed4966d39c467",
        },
        "7/declaration/body/8/argument/15/1/3": {
          content: {
            en: "Pre-built reports tailored for family financial management",
          },
          hash: "e37b3efdee90d02eb120514768126e87",
        },
        "7/declaration/body/8/argument/15/3/1/1/expression/0/body/3/1/11": {
          content: {
            en: "Last generated: <function:Date.toLocaleDateString/>",
          },
          hash: "6ff9bc91b84c0c3f026dbc620af170b4",
        },
        "7/declaration/body/8/argument/15/3/1/1/expression/0/body/3/1/5/1": {
          content: {
            en: "Key Insights",
          },
          hash: "69a88b6bb03978b01c3f27420f215135",
        },
        "7/declaration/body/8/argument/15/3/1/1/expression/0/body/3/1/9/1": {
          content: {
            en: "View Report",
          },
          hash: "eed70d1e0467b3342c740bf29a6d50d0",
        },
        "7/declaration/body/8/argument/15/3/1/1/expression/0/body/3/1/9/3": {
          content: {
            en: "<element:Download></element:Download> Export",
          },
          hash: "1e74b6f596fbedc0d2e83d690ce7e2bf",
        },
        "7/declaration/body/8/argument/19/1/1/3/1": {
          content: {
            en: "Custom Report Builder",
          },
          hash: "ebc30f9c8dda22c384003435a7c2c93a",
        },
        "7/declaration/body/8/argument/19/1/1/3/3": {
          content: {
            en: "Create custom reports tailored to your family's specific needs. Choose from various data sources, time periods, and visualization options to get the insights that matter most.",
          },
          hash: "904cf9bd103bb0d8acd9d1d4ed33294c",
        },
        "7/declaration/body/8/argument/19/1/1/3/5/1": {
          content: {
            en: "Build Custom Report",
          },
          hash: "1993f1b0d2eb684706f2e57f417a5eae",
        },
        "7/declaration/body/8/argument/19/1/1/3/5/3": {
          content: {
            en: "Charts & Graphs",
          },
          hash: "ea4c737d7427dccdff33a73ae223af52",
        },
        "7/declaration/body/8/argument/19/1/1/3/5/5": {
          content: {
            en: "Export Options",
          },
          hash: "c6565750149b1941d92e5b4131211720",
        },
        "7/declaration/body/8/argument/19/1/1/3/5/7": {
          content: {
            en: "Scheduled Reports",
          },
          hash: "e8c5b891f1d0898fda1123900bcfecdf",
        },
        "7/declaration/body/8/argument/3/1/1": {
          content: {
            en: "Family Reports",
          },
          hash: "270300fff029e3399b56ff080b08609c",
        },
        "7/declaration/body/8/argument/3/1/3": {
          content: {
            en: "Comprehensive financial insights for your family",
          },
          hash: "a3d7e114bef76783ecda81233afdb17b",
        },
        "7/declaration/body/8/argument/3/3/1": {
          content: {
            en: "<element:Share2></element:Share2> Share Reports",
          },
          hash: "227dd4070caee4fd79852ca6f601f415",
        },
        "7/declaration/body/8/argument/3/3/3": {
          content: {
            en: "<element:Download></element:Download> Export All",
          },
          hash: "86a5fe516498e0cdf327931c5cda0246",
        },
        "7/declaration/body/8/argument/7/1/1": {
          content: {
            en: "<element:Filter></element:Filter> Report Filters",
          },
          hash: "5511c56843e0e45b8fd637a1c682db45",
        },
        "7/declaration/body/8/argument/7/3/1/1/1": {
          content: {
            en: "Time Period",
          },
          hash: "beb4353e8d4815fb17d4038eb94909d0",
        },
        "7/declaration/body/8/argument/7/3/1/1/3/1/1-placeholder": {
          content: {
            en: "Select period",
          },
          hash: "f3e492e15b52807eda6248fcdd65e36f",
        },
        "7/declaration/body/8/argument/7/3/1/1/3/3/1": {
          content: {
            en: "Last Week",
          },
          hash: "94fb11d2c925631adb321efe1490594a",
        },
        "7/declaration/body/8/argument/7/3/1/1/3/3/3": {
          content: {
            en: "Last Month",
          },
          hash: "9ab119dff0cb00d094cef2066572fe92",
        },
        "7/declaration/body/8/argument/7/3/1/1/3/3/5": {
          content: {
            en: "Last Quarter",
          },
          hash: "eeb5d21412746c43b7623e0fd05ed9b0",
        },
        "7/declaration/body/8/argument/7/3/1/1/3/3/7": {
          content: {
            en: "Last Year",
          },
          hash: "42547108d452358d115c59f8e663e0a5",
        },
        "7/declaration/body/8/argument/7/3/1/1/3/3/9": {
          content: {
            en: "Custom Range",
          },
          hash: "99f4d72b64621406acc162cceeb1fed7",
        },
        "7/declaration/body/8/argument/7/3/1/3/1": {
          content: {
            en: "Family Member",
          },
          hash: "b72fd922e4b4ea2e97cff03a5a3fcbdf",
        },
        "7/declaration/body/8/argument/7/3/1/3/3/1/1-placeholder": {
          content: {
            en: "Select member",
          },
          hash: "7f4a38312aabbbe3fe92756b57bd5d75",
        },
        "7/declaration/body/8/argument/7/3/1/3/3/3/1": {
          content: {
            en: "All Members",
          },
          hash: "2e791629e0b2d773d351738c0c208b4b",
        },
        "7/declaration/body/8/argument/7/3/1/3/3/3/3": {
          content: {
            en: "John",
          },
          hash: "41853d3f04b645aa5d16b5faa5cd7a96",
        },
        "7/declaration/body/8/argument/7/3/1/3/3/3/5": {
          content: {
            en: "Sarah",
          },
          hash: "c22d5873343126c5cfa23ff7a45e5dc0",
        },
        "7/declaration/body/8/argument/7/3/1/3/3/3/7": {
          content: {
            en: "Emma",
          },
          hash: "131a06772255828fc725f0c765fe0f92",
        },
        "7/declaration/body/8/argument/7/3/1/3/3/3/9": {
          content: {
            en: "Mike",
          },
          hash: "26487045bb9a6048c425467ac02bd165",
        },
        "7/declaration/body/8/argument/7/3/1/5/1": {
          content: {
            en: "Generate Report",
          },
          hash: "8f20da23bb7692f803a254cb5f99040e",
        },
      },
    },
    "app/beta/dashboard/page.tsx": {
      entries: {
        "9/declaration/body/7/consequent/0/argument/3/1/3": {
          content: {
            en: "Your Personal Finance Dashboard - Real data, powerful insights",
          },
          hash: "3bd9a916d70d452cc553bba6d279d0f2",
        },
        "9/declaration/body/7/consequent/0/argument/3/3/1": {
          content: {
            en: "<element:Sparkles></element:Sparkles> Personal Finance Beta",
          },
          hash: "a06345e2968654e8e9174d2ca4636768",
        },
        "9/declaration/body/7/consequent/0/argument/7/1/1": {
          content: {
            en: "<element:Home></element:Home> Overview",
          },
          hash: "97d979a3267bfe1889f3fda8331cff39",
        },
        "9/declaration/body/7/consequent/0/argument/7/1/3": {
          content: {
            en: "<element:Target></element:Target> Budgets",
          },
          hash: "600954107dbf1531aa019ad6f86c0b66",
        },
        "9/declaration/body/7/consequent/0/argument/7/1/5": {
          content: {
            en: "<element:Star></element:Star> Goals",
          },
          hash: "f0e1d4acb89aba2a0f7329507c26d087",
        },
        "9/declaration/body/7/consequent/0/argument/7/1/7": {
          content: {
            en: "<element:Wallet></element:Wallet> Transactions",
          },
          hash: "cab7ff2f6e4c42d7c17984bd83471c6c",
        },
        "9/declaration/body/8/argument/1/1/3": {
          content: {
            en: "<expression/>  - Coming Soon",
          },
          hash: "8b881fc578abb5b6a5ccc8637339c8f8",
        },
        "9/declaration/body/8/argument/1/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> Beta Preview",
          },
          hash: "161b0e0d866f9a8a1047a3ce8db48293",
        },
        "9/declaration/body/8/argument/3/1/5": {
          content: {
            en: "This dashboard is currently in development. Switch to Personal Finance to explore the completed features.",
          },
          hash: "c9015f1e357077114919f5c98468c2e2",
        },
        "9/declaration/body/8/argument/3/1/7/1": {
          content: {
            en: "<element:ArrowRight></element:ArrowRight> Try Personal Finance",
          },
          hash: "11dd09947613fb02f95af19fb35c9f7d",
        },
        "9/declaration/body/8/argument/3/1/7/3/openingElement/0/value/expression":
          {
            content: {
              en: "<element:MessageCircle></element:MessageCircle> Share Feedback",
            },
            hash: "8bcfbbb15ed197e07804ab2495f5dfec",
          },
      },
    },
    "app/beta/dashboard/personal/accounts/page.tsx": {
      entries: {
        "11/declaration/body/12/consequent/0/argument/1/1/1": {
          content: {
            en: "Personal Accounts",
          },
          hash: "923ca8a20857b7fffec8ef38db95f4c6",
        },
        "11/declaration/body/12/consequent/0/argument/1/1/3": {
          content: {
            en: "Manage your individual accounts",
          },
          hash: "06671f8395b20089d626675b8905ce99",
        },
        "11/declaration/body/13/argument/11/1/3-placeholder": {
          content: {
            en: "Search accounts...",
          },
          hash: "fe4da735fd715be79d4f3054246266e6",
        },
        "11/declaration/body/13/argument/15/expression/alternate/1/expression/0/body/1/3/3/1":
          {
            content: {
              en: "Edit Account",
            },
            hash: "3cffb9004fec948e5b6426c8b13c47ea",
          },
        "11/declaration/body/13/argument/15/expression/alternate/1/expression/0/body/1/3/3/3":
          {
            content: {
              en: "View Transactions",
            },
            hash: "5f40ba6a896e6e18b910be628ee6e9f2",
          },
        "11/declaration/body/13/argument/15/expression/alternate/1/expression/0/body/1/3/3/5":
          {
            content: {
              en: "Delete Account",
            },
            hash: "2311aec96f9a2bbf3d82c8074ee43a9b",
          },
        "11/declaration/body/13/argument/15/expression/alternate/1/expression/0/body/3/1/1/1":
          {
            content: {
              en: "Current Balance",
            },
            hash: "f870a7f61fa05a5e474a0041fb732273",
          },
        "11/declaration/body/13/argument/15/expression/alternate/1/expression/0/body/3/1/3/expression/right/1":
          {
            content: {
              en: "Institution",
            },
            hash: "57d6a0aa8e6dde28843ca08fb96a7fdd",
          },
        "11/declaration/body/13/argument/15/expression/alternate/1/expression/0/body/3/1/5/1":
          {
            content: {
              en: "Status",
            },
            hash: "4e1fcce15854d824919b4a582c697c90",
          },
        "11/declaration/body/13/argument/15/expression/alternate/1/expression/0/body/3/1/7/expression/right":
          {
            content: {
              en: "Last synced: <function:Date.toLocaleDateString/>",
            },
            hash: "cc69210e5b8e46c5853c3d9c28fff508",
          },
        "11/declaration/body/13/argument/15/expression/consequent/1/3": {
          content: {
            en: "No Accounts Found",
          },
          hash: "10af825438817857e97f8a7d73991f7a",
        },
        "11/declaration/body/13/argument/15/expression/consequent/1/7": {
          content: {
            en: "<element:Plus></element:Plus> Add Your First Account",
          },
          hash: "dd2f936988b6e0b24fca8795602f5b24",
        },
        "11/declaration/body/13/argument/3/1/1": {
          content: {
            en: "Personal Accounts",
          },
          hash: "f584c3902d26689d4f322e5d906792e2",
        },
        "11/declaration/body/13/argument/3/1/3": {
          content: {
            en: "Manage your individual financial accounts",
          },
          hash: "76d9f4354e37c005fb02d7b30f2c3514",
        },
        "11/declaration/body/13/argument/3/3/1": {
          content: {
            en: "<expression/><expression/> Balances",
          },
          hash: "f3d7f22f98f08375e2484b2170a90b17",
        },
        "11/declaration/body/13/argument/3/3/3": {
          content: {
            en: "<element:Plus></element:Plus> Add Account",
          },
          hash: "b21d2942059a36e534d810ba6de30c75",
        },
        "11/declaration/body/13/argument/7/1/1": {
          content: {
            en: "<element:Wallet></element:Wallet> Account Summary",
          },
          hash: "18d241b26d088681dc04d2e3ae4795ce",
        },
        "11/declaration/body/13/argument/7/3/1/1/1": {
          content: {
            en: "Total Balance",
          },
          hash: "fd1144e816cfaf16c396da0540d57f8c",
        },
        "11/declaration/body/13/argument/7/3/1/3/1": {
          content: {
            en: "Active Accounts",
          },
          hash: "1d4804de0a99c131967d51bd4067f2b0",
        },
        "11/declaration/body/13/argument/7/3/1/5/1": {
          content: {
            en: "Account Types",
          },
          hash: "4f20ff3adb49e178599308f3dc4292de",
        },
      },
    },
    "app/beta/dashboard/personal/analytics/page.tsx": {
      entries: {
        "8/declaration/body/7/consequent/0/argument/1/1/1": {
          content: {
            en: "Personal Analytics",
          },
          hash: "b6510c389898e70f6bf2b854021ac190",
        },
        "8/declaration/body/7/consequent/0/argument/1/1/3": {
          content: {
            en: "Insights into your financial patterns",
          },
          hash: "4cb9b5f00783a7e82f119e504a7d20df",
        },
        "8/declaration/body/8/argument/11/1/1/1": {
          content: {
            en: "Monthly Income",
          },
          hash: "f0d770bdda6bc1e8847686d1c8ac8997",
        },
        "8/declaration/body/8/argument/11/1/3/3": {
          content: {
            en: "+5.2% from last month",
          },
          hash: "4b0d12b0cf2bfe3d2d52c20473806f62",
        },
        "8/declaration/body/8/argument/11/3/1/1": {
          content: {
            en: "Monthly Expenses",
          },
          hash: "cfd957ffdc837c0851bdc307a220f692",
        },
        "8/declaration/body/8/argument/11/3/3/3": {
          content: {
            en: "-2.1% from last month",
          },
          hash: "f6b1c03b7d96a6bd1febd62a76068f65",
        },
        "8/declaration/body/8/argument/11/5/1/1": {
          content: {
            en: "Savings Rate",
          },
          hash: "32bbceface3004ce15d7fc932f366230",
        },
        "8/declaration/body/8/argument/11/5/3/1": {
          content: {
            en: "<expression/>%",
          },
          hash: "f0b7f727bc805a82ffd8099388cd415d",
        },
        "8/declaration/body/8/argument/11/5/3/3": {
          content: {
            en: "Above recommended 20%",
          },
          hash: "0adb32160518baeda9092699aaba3fac",
        },
        "8/declaration/body/8/argument/11/7/1/1": {
          content: {
            en: "Budget Efficiency",
          },
          hash: "9fbfd99c998b17f7254e2c62a12fe985",
        },
        "8/declaration/body/8/argument/11/7/3/1": {
          content: {
            en: "<expression/>%",
          },
          hash: "f0b7f727bc805a82ffd8099388cd415d",
        },
        "8/declaration/body/8/argument/11/7/3/3": {
          content: {
            en: "Staying within budgets",
          },
          hash: "e70b3ce977fae0b26936073de8abe9e1",
        },
        "8/declaration/body/8/argument/15/3/1/1": {
          content: {
            en: "<element:PieChart></element:PieChart> Spending by Category",
          },
          hash: "a21c9f676a725a7d2804fc813bd4aa50",
        },
        "8/declaration/body/8/argument/15/3/1/3": {
          content: {
            en: "Your top spending categories this month",
          },
          hash: "a3097f3e9d9abdd72613607957be6002",
        },
        "8/declaration/body/8/argument/15/3/3/1/1/expression/left/0/body/3/3": {
          content: {
            en: "{category.percentage}%",
          },
          hash: "815b69e2ba7c258c55d7390d6ff1fc1c",
        },
        "8/declaration/body/8/argument/15/3/3/1/1/expression/right/3": {
          content: {
            en: "No spending data available",
          },
          hash: "0d1847ec32a373af5696f112b5164f57",
        },
        "8/declaration/body/8/argument/15/7/1/1": {
          content: {
            en: "<element:BarChart3></element:BarChart3> Monthly Trends",
          },
          hash: "6d9ba7a405dd5afbbd85b514e3b5219b",
        },
        "8/declaration/body/8/argument/15/7/1/3": {
          content: {
            en: "Income vs expenses over time",
          },
          hash: "4b6bbee98af90ca99b08c142e18fef1f",
        },
        "8/declaration/body/8/argument/15/7/3/1/1/3": {
          content: {
            en: "Interactive charts coming soon",
          },
          hash: "c6c691f698a0f9796f36adf35eae45cf",
        },
        "8/declaration/body/8/argument/15/7/3/1/1/5": {
          content: {
            en: "Beta Feature",
          },
          hash: "4d742cfbe2b553fa4631f20bd0d0a320",
        },
        "8/declaration/body/8/argument/19/1/1": {
          content: {
            en: "<element:Sparkles></element:Sparkles> AI Financial Insights",
          },
          hash: "5449604e9e334db7b2d0227f5f4fded0",
        },
        "8/declaration/body/8/argument/19/1/3": {
          content: {
            en: "Personalized recommendations based on your financial patterns",
          },
          hash: "50ae687d0015a4710f3ad2679e7578b1",
        },
        "8/declaration/body/8/argument/19/3/1/1/1/3/1": {
          content: {
            en: "Great Savings Progress!",
          },
          hash: "c57ef821c675b6d3c281d6e8c399d1d3",
        },
        "8/declaration/body/8/argument/19/3/1/1/1/3/3": {
          content: {
            en: "You're saving 25% of your income this month, which is above the recommended 20%. Consider increasing your emergency fund or exploring investment opportunities.",
          },
          hash: "ce7f1eae46127e46fb2dbb7c41410d52",
        },
        "8/declaration/body/8/argument/19/3/1/3/1/3/1": {
          content: {
            en: "Dining Out Trend",
          },
          hash: "d4a41be8dd46017eb5984cf3dc4cdc61",
        },
        "8/declaration/body/8/argument/19/3/1/3/1/3/3": {
          content: {
            en: "Your dining expenses have increased by 30% this month. Consider meal planning or setting a dining budget to maintain your savings goals.",
          },
          hash: "4b17388a12c1c77e8d7813fe1537ce16",
        },
        "8/declaration/body/8/argument/19/3/1/5/1/3/1": {
          content: {
            en: "Budget Optimization",
          },
          hash: "c75dfb11c265933311f64102bd01de08",
        },
        "8/declaration/body/8/argument/19/3/1/5/1/3/3": {
          content: {
            en: "Based on your spending patterns, you could reallocate $200 from entertainment to investments without impacting your lifestyle significantly.",
          },
          hash: "eeed8ec8af81e662d7164304cad71ec2",
        },
        "8/declaration/body/8/argument/3/1/1": {
          content: {
            en: "Personal Analytics",
          },
          hash: "6c02d58a4776b663f4a06cee41c81b5c",
        },
        "8/declaration/body/8/argument/3/1/3": {
          content: {
            en: "Discover insights and patterns in your personal finances",
          },
          hash: "fc1551e1131d12e8bbe782e1414937d5",
        },
        "8/declaration/body/8/argument/3/3/1": {
          content: {
            en: "<element:Sparkles></element:Sparkles> AI Insights",
          },
          hash: "106e2b59f31440af2acc2af71352d8c4",
        },
        "8/declaration/body/8/argument/7/1/1/3/1": {
          content: {
            en: "AI-Powered Financial Insights - New!",
          },
          hash: "1d27e3a1c3ba80ecda8ad86a8ac78ef3",
        },
        "8/declaration/body/8/argument/7/1/1/3/3": {
          content: {
            en: "Our beta AI analyzes your spending patterns, predicts future trends, and provides personalized recommendations to optimize your financial health.",
          },
          hash: "635d3228d3a9d9c16ac448fcb6dfa487",
        },
        "8/declaration/body/8/argument/7/1/1/3/5/1": {
          content: {
            en: "Spending Patterns",
          },
          hash: "1227632889c12fe958507bcf221dc928",
        },
        "8/declaration/body/8/argument/7/1/1/3/5/3": {
          content: {
            en: "Trend Analysis",
          },
          hash: "fd297d21227591b03c484f1bef7a8a69",
        },
        "8/declaration/body/8/argument/7/1/1/3/5/5": {
          content: {
            en: "Smart Predictions",
          },
          hash: "3b14d8d3f6d29f9593efa90646ee0d51",
        },
      },
    },
    "app/beta/dashboard/personal/budgets/page.tsx": {
      entries: {
        "10/declaration/body/13/consequent/0/argument/1/1/1": {
          content: {
            en: "Personal Budgets",
          },
          hash: "ab402bb8f213767b74462960de654ab2",
        },
        "10/declaration/body/13/consequent/0/argument/1/1/3": {
          content: {
            en: "Manage your personal spending limits",
          },
          hash: "4b4360020845302f3650ccc9ddd49c69",
        },
        "10/declaration/body/14/argument/11/1/1/3/1": {
          content: {
            en: "Smart Budget Alerts - Beta Feature",
          },
          hash: "11d007868585279c2af543a1f95ddad9",
        },
        "10/declaration/body/14/argument/11/1/1/3/3": {
          content: {
            en: "Your budgets now include intelligent spending pattern analysis and predictive alerts. This helps you stay on track with personalized recommendations.",
          },
          hash: "2edcd59d628c6e1f46429d3729a14b9f",
        },
        "10/declaration/body/14/argument/11/1/1/3/5": {
          content: {
            en: "Coming Soon: AI Budget Optimization",
          },
          hash: "62f0d5ee6e8f5add7d224c3a9c0950d1",
        },
        "10/declaration/body/14/argument/15/expression/alternate/1/expression/0/body/3/1/1/1":
          {
            content: {
              en: "Progress",
            },
            hash: "dd0200d5849ebb7d64c15098ae91d229",
          },
        "10/declaration/body/14/argument/15/expression/alternate/1/expression/0/body/3/1/1/3":
          {
            content: {
              en: "<function:budget.percentage_used.toFixed/>%",
            },
            hash: "5ffe4a4526d680df3dc6d6e7880cbc9a",
          },
        "10/declaration/body/14/argument/15/expression/alternate/1/expression/0/body/3/3/1/1":
          {
            content: {
              en: "Budgeted",
            },
            hash: "6a4b178b97bef0e21fed6fc88aab4293",
          },
        "10/declaration/body/14/argument/15/expression/alternate/1/expression/0/body/3/3/3/1":
          {
            content: {
              en: "Spent",
            },
            hash: "73701d4afa57c184d8afde7aa31e47a7",
          },
        "10/declaration/body/14/argument/15/expression/alternate/1/expression/0/body/3/5/1/1":
          {
            content: {
              en: "Remaining",
            },
            hash: "69f8e1938a53866199a25ff2c22849b6",
          },
        "10/declaration/body/14/argument/15/expression/alternate/1/expression/0/body/3/7/expression/right/1/3":
          {
            content: {
              en: "You've used <function:budget.percentage_used.toFixed/>% of this budget. Consider reviewing your spending.",
            },
            hash: "3a3b855975fdc94d59505e6d13f80ac6",
          },
        "10/declaration/body/14/argument/15/expression/alternate/1/expression/0/body/3/9/expression/right/1/3":
          {
            content: {
              en: "Budget exceeded by <function:formatCurrency/>. Time to adjust your spending or budget.",
            },
            hash: "6518bfcb04e4ca6e1328c8313fb5f3cf",
          },
        "10/declaration/body/14/argument/15/expression/consequent/1/3": {
          content: {
            en: "No Budgets Yet",
          },
          hash: "c09087809e6617c4699c4ac3b9deea47",
        },
        "10/declaration/body/14/argument/15/expression/consequent/1/5": {
          content: {
            en: "Create your first budget to start tracking your spending and achieving your financial goals.",
          },
          hash: "d97058244cfab260ae22566a26305a82",
        },
        "10/declaration/body/14/argument/15/expression/consequent/1/7": {
          content: {
            en: "<element:Plus></element:Plus> Create Your First Budget",
          },
          hash: "8c3cf523551886b5e8231d19f62d5149",
        },
        "10/declaration/body/14/argument/3/1/1": {
          content: {
            en: "Personal Budgets",
          },
          hash: "f78144b57cb46e83a3be70e5526371aa",
        },
        "10/declaration/body/14/argument/3/1/3": {
          content: {
            en: "Set spending limits and track your progress toward financial goals",
          },
          hash: "962a32945db5c8ec0cc89d55c21439aa",
        },
        "10/declaration/body/14/argument/3/3": {
          content: {
            en: "<element:Plus></element:Plus> Create Budget",
          },
          hash: "8ec7c6fff18fa50f3d7d09fab43a4571",
        },
        "10/declaration/body/14/argument/7/1/1/1": {
          content: {
            en: "Total Budgeted",
          },
          hash: "cf73357da34965a9a858cffff4e3150f",
        },
        "10/declaration/body/14/argument/7/1/3/3": {
          content: {
            en: "Across {budgets.length} budgets",
          },
          hash: "e03cda4420e6caa56f25f4096eef7d85",
        },
        "10/declaration/body/14/argument/7/3/1/1": {
          content: {
            en: "Total Spent",
          },
          hash: "006d95dd6507afe98601c460793c5158",
        },
        "10/declaration/body/14/argument/7/5/1/1": {
          content: {
            en: "Remaining",
          },
          hash: "5db6eb99c266450c802a2bd345e5dce0",
        },
        "10/declaration/body/14/argument/7/5/3/3": {
          content: {
            en: "Available to spend",
          },
          hash: "76fec993c0d76c2d91ee4067658df1e3",
        },
      },
    },
    "app/beta/dashboard/personal/goals/page.tsx": {
      entries: {
        "10/declaration/body/14/consequent/0/argument/1/1/1": {
          content: {
            en: "Personal Goals",
          },
          hash: "fefe935dfcecee9d212ddef3ca8e6b68",
        },
        "10/declaration/body/14/consequent/0/argument/1/1/3": {
          content: {
            en: "Track your financial objectives",
          },
          hash: "766bcf8a4e8575d4aa813718e8b61d8d",
        },
        "10/declaration/body/15/argument/11/expression/alternate/1/expression/0/body/2/argument/3/1/1/1":
          {
            content: {
              en: "Progress",
            },
            hash: "dd0200d5849ebb7d64c15098ae91d229",
          },
        "10/declaration/body/15/argument/11/expression/alternate/1/expression/0/body/2/argument/3/1/1/3":
          {
            content: {
              en: "<function:goal.progress_percentage.toFixed/>%",
            },
            hash: "5ffe4a4526d680df3dc6d6e7880cbc9a",
          },
        "10/declaration/body/15/argument/11/expression/alternate/1/expression/0/body/2/argument/3/11/expression/right/1":
          {
            content: {
              en: "This goal is <element:strong><function:Math.abs/> days overdue</element:strong>. Consider adjusting the target date or amount.",
            },
            hash: "f10d6c1ee437a066aebd2b96822bf0ce",
          },
        "10/declaration/body/15/argument/11/expression/alternate/1/expression/0/body/2/argument/3/13/expression/right/1/3":
          {
            content: {
              en: "Congratulations! Goal completed!",
            },
            hash: "df4150a626d4be702d2036b0f1a0b5df",
          },
        "10/declaration/body/15/argument/11/expression/alternate/1/expression/0/body/2/argument/3/3/1/1":
          {
            content: {
              en: "Current",
            },
            hash: "27f172f76ac28e72cb062f80002b0ad5",
          },
        "10/declaration/body/15/argument/11/expression/alternate/1/expression/0/body/2/argument/3/3/3/1":
          {
            content: {
              en: "Target",
            },
            hash: "569e5f09543453de7b2dde95e4378261",
          },
        "10/declaration/body/15/argument/11/expression/alternate/1/expression/0/body/2/argument/3/5/1/1":
          {
            content: {
              en: "Remaining",
            },
            hash: "5db6eb99c266450c802a2bd345e5dce0",
          },
        "10/declaration/body/15/argument/11/expression/alternate/1/expression/0/body/2/argument/3/7/3":
          {
            content: {
              en: "Target: <function:formatDate/>",
            },
            hash: "064993af27167e88b0efe63d0c8fff31",
          },
        "10/declaration/body/15/argument/11/expression/alternate/1/expression/0/body/2/argument/3/9/expression/right/1":
          {
            content: {
              en: "<element:strong>{daysRemaining} days</element:strong> remaining to reach your goal<expression/>",
            },
            hash: "47557c0f4397aa3b738768c8bac33c53",
          },
        "10/declaration/body/15/argument/11/expression/consequent/1/3": {
          content: {
            en: "No Goals Yet",
          },
          hash: "ed17110f15a91d3cb6aace23a30ef867",
        },
        "10/declaration/body/15/argument/11/expression/consequent/1/5": {
          content: {
            en: "Create your first financial goal to start working toward your dreams and objectives.",
          },
          hash: "da06498d63326ab10d12a1a674f7083f",
        },
        "10/declaration/body/15/argument/11/expression/consequent/1/7": {
          content: {
            en: "<element:Plus></element:Plus> Create Your First Goal",
          },
          hash: "7dfc77991c2d5c878f5f11749d698cb0",
        },
        "10/declaration/body/15/argument/3/1/1": {
          content: {
            en: "Personal Goals",
          },
          hash: "3abbb78e951c34ed38b7c2dee568e714",
        },
        "10/declaration/body/15/argument/3/1/3": {
          content: {
            en: "Set and track your financial objectives and milestones",
          },
          hash: "3db875e8e31b1a813c5f4efb14797540",
        },
        "10/declaration/body/15/argument/3/3": {
          content: {
            en: "<element:Plus></element:Plus> Create Goal",
          },
          hash: "425849fff65bc9384c9788c947e04054",
        },
        "10/declaration/body/15/argument/7/1/1/1": {
          content: {
            en: "Total Goal Value",
          },
          hash: "01da64f5305aebd01fed5ddfba383e0b",
        },
        "10/declaration/body/15/argument/7/1/3/3": {
          content: {
            en: "Across {goals.length} goals",
          },
          hash: "3797b98d3a435d4e7ae38128dc6ba8de",
        },
        "10/declaration/body/15/argument/7/3/1/1": {
          content: {
            en: "Progress Amount",
          },
          hash: "10b28b5bf65d6513568e25a77f8d4081",
        },
        "10/declaration/body/15/argument/7/5/1/1": {
          content: {
            en: "Active Goals",
          },
          hash: "5a4cd2daefe4889a113ebcec336fa50a",
        },
        "10/declaration/body/15/argument/7/5/3/3": {
          content: {
            en: "Currently working on",
          },
          hash: "f4b002231ca541205590546511396018",
        },
      },
    },
    "app/beta/dashboard/personal/page.tsx": {
      entries: {
        "9/declaration/body/7/argument/11/1/1": {
          content: {
            en: "<element:BarChart3></element:BarChart3> Overview",
          },
          hash: "6d7d625b84c5de6649c07c4a53c0bb94",
        },
        "9/declaration/body/7/argument/11/1/3": {
          content: {
            en: "<element:PieChart></element:PieChart> Budgets",
          },
          hash: "f8388d197e20b55fba7d1510ddbfa5f4",
        },
        "9/declaration/body/7/argument/11/1/5": {
          content: {
            en: "<element:Target></element:Target> Goals",
          },
          hash: "0ea765e7644d1445564a167111b22573",
        },
        "9/declaration/body/7/argument/11/1/7": {
          content: {
            en: "<element:CreditCard></element:CreditCard> Transactions",
          },
          hash: "d4b44894388e85ea22dbe67143e2cc4b",
        },
        "9/declaration/body/7/argument/3/1/3": {
          content: {
            en: "Your personal financial dashboard - clean, focused, and designed just for you",
          },
          hash: "bc001cb0491f451b44fce608865993fe",
        },
        "9/declaration/body/7/argument/3/3/1": {
          content: {
            en: "<element:Sparkles></element:Sparkles> Personal Finance Beta",
          },
          hash: "83b0a0d165f8232f5991a547054a4520",
        },
        "9/declaration/body/7/argument/3/3/3/openingElement/0/value/expression":
          {
            content: {
              en: "<element:MessageCircle></element:MessageCircle> Feedback",
            },
            hash: "9dbaa5dae75fff7a7c16ce4bc054e468",
          },
        "9/declaration/body/7/argument/7/1/1/3/1": {
          content: {
            en: "Welcome to Personal Finance Beta!",
          },
          hash: "e1ceb04ec7dc920e17d2457050d7a8a6",
        },
        "9/declaration/body/7/argument/7/1/1/3/3": {
          content: {
            en: "You're experiencing a simplified, individual-focused financial management interface. This dashboard is optimized for personal use with 50% fewer distractions.",
          },
          hash: "42bea7df5c051f0cbe77064c7def70d7",
        },
        "9/declaration/body/7/argument/7/1/1/3/5/1": {
          content: {
            en: "Clean Interface",
          },
          hash: "99be449eab26f4d623c724ce38f04275",
        },
        "9/declaration/body/7/argument/7/1/1/3/5/3": {
          content: {
            en: "Fast Actions",
          },
          hash: "dd776a89080b0985e341c4d910c31511",
        },
        "9/declaration/body/7/argument/7/1/1/3/5/5": {
          content: {
            en: "Personal Focus",
          },
          hash: "e6b2e9a071cc1c355fbe7d4a434eacfa",
        },
      },
    },
    "app/beta/dashboard/personal/transactions/page.tsx": {
      entries: {
        "12/declaration/body/17/consequent/0/argument/1/1/1": {
          content: {
            en: "Personal Transactions",
          },
          hash: "a7d1e50b848cb5ae710c0eb4cf681666",
        },
        "12/declaration/body/17/consequent/0/argument/1/1/3": {
          content: {
            en: "Track your personal income and expenses",
          },
          hash: "8122a79e22bd3d37c56a23b197e92aa5",
        },
        "12/declaration/body/18/argument/11/1/1": {
          content: {
            en: "Filter Transactions",
          },
          hash: "a6cbaa1d649d65e367c6f0517e649c01",
        },
        "12/declaration/body/18/argument/11/3/1/1/3-placeholder": {
          content: {
            en: "Search transactions...",
          },
          hash: "e23db6cacfff0bc3af38ce44da9f410a",
        },
        "12/declaration/body/18/argument/11/3/1/3/1/1-placeholder": {
          content: {
            en: "Type",
          },
          hash: "f04471a7ddac844b9ad145eb9911ef75",
        },
        "12/declaration/body/18/argument/11/3/1/3/3/1": {
          content: {
            en: "All Types",
          },
          hash: "32b4b35f93cc4c04ba5a5d2192340a99",
        },
        "12/declaration/body/18/argument/11/3/1/3/3/3": {
          content: {
            en: "Income",
          },
          hash: "398bff8476817f942c70c4aadad5dd91",
        },
        "12/declaration/body/18/argument/11/3/1/3/3/5": {
          content: {
            en: "Expenses",
          },
          hash: "1746536359bb638c45a12980ab3aac3a",
        },
        "12/declaration/body/18/argument/11/3/1/5/1/1-placeholder": {
          content: {
            en: "Category",
          },
          hash: "1b0340cd175aa5a2be745a0d54908466",
        },
        "12/declaration/body/18/argument/11/3/1/5/3/1": {
          content: {
            en: "All Categories",
          },
          hash: "2547e684d62a4710bdfbb9415a9970d0",
        },
        "12/declaration/body/18/argument/15/1/1": {
          content: {
            en: "<element:ArrowRightLeft></element:ArrowRightLeft> Recent Transactions<element:Badge>{filteredTransactions.length} transactions</element:Badge>",
          },
          hash: "9e0fcd42cfec3f04f73465bad731361e",
        },
        "12/declaration/body/18/argument/15/3/1/expression/alternate/1/1/1": {
          content: {
            en: "Date",
          },
          hash: "56f41c5d30a76295bb087b20b7bee4c3",
        },
        "12/declaration/body/18/argument/15/3/1/expression/alternate/1/1/3": {
          content: {
            en: "Description",
          },
          hash: "e17686a22ffad04cc7bb70524ed4478b",
        },
        "12/declaration/body/18/argument/15/3/1/expression/alternate/1/1/5": {
          content: {
            en: "Category",
          },
          hash: "1b0340cd175aa5a2be745a0d54908466",
        },
        "12/declaration/body/18/argument/15/3/1/expression/alternate/1/1/7": {
          content: {
            en: "Account",
          },
          hash: "01215c12fb1cdb93bd0c84c1382bef56",
        },
        "12/declaration/body/18/argument/15/3/1/expression/alternate/1/1/9": {
          content: {
            en: "Amount",
          },
          hash: "a0ae9c6ff98b57475cdce56f66066aa4",
        },
        "12/declaration/body/18/argument/15/3/1/expression/consequent/3": {
          content: {
            en: "No Transactions Found",
          },
          hash: "f9d51f55e60a4beee92fa70f347e18b0",
        },
        "12/declaration/body/18/argument/15/3/1/expression/consequent/7": {
          content: {
            en: "<element:Plus></element:Plus> Add Transaction",
          },
          hash: "96187f820c51324883276a5abeb2722b",
        },
        "12/declaration/body/18/argument/3/1/1": {
          content: {
            en: "Personal Transactions",
          },
          hash: "0d881819f9f0a4a339e071b004423a70",
        },
        "12/declaration/body/18/argument/3/1/3": {
          content: {
            en: "Track and manage your personal income and expenses",
          },
          hash: "75085394ed4dd1bc6ed74e0256233c5a",
        },
        "12/declaration/body/18/argument/3/3/1": {
          content: {
            en: "<element:Download></element:Download> Export",
          },
          hash: "82c7fb0a52965a8cde48deeee9cd663b",
        },
        "12/declaration/body/18/argument/3/3/3": {
          content: {
            en: "<element:Plus></element:Plus> Add Transaction",
          },
          hash: "7482795a6ddf35beeb51ed7ba77da9b9",
        },
        "12/declaration/body/18/argument/7/1/1/1": {
          content: {
            en: "Total Income",
          },
          hash: "d96c55cd509ed55f6dfbf78dbcf0605d",
        },
        "12/declaration/body/18/argument/7/1/3/3": {
          content: {
            en: "This period",
          },
          hash: "d1d310f422491b039c1640550cf91a80",
        },
        "12/declaration/body/18/argument/7/3/1/1": {
          content: {
            en: "Total Expenses",
          },
          hash: "57bfc5bd4e97342446e0423f6a9c0fd0",
        },
        "12/declaration/body/18/argument/7/3/3/3": {
          content: {
            en: "This period",
          },
          hash: "d1d310f422491b039c1640550cf91a80",
        },
        "12/declaration/body/18/argument/7/5/1/1": {
          content: {
            en: "Net Cash Flow",
          },
          hash: "821ffc060c7a8d3a051a03eb8d142d7a",
        },
        "12/declaration/body/18/argument/7/5/3/3": {
          content: {
            en: "This period",
          },
          hash: "d1d310f422491b039c1640550cf91a80",
        },
      },
    },
    "app/beta/dashboard/settings/page.tsx": {
      entries: {
        "14/declaration/body/7/consequent/0/argument/1/3": {
          content: {
            en: "Loading your settings...",
          },
          hash: "5aed2160b61a995548c87ab27e626112",
        },
        "14/declaration/body/8/argument/11/1/1": {
          content: {
            en: "<element:Shield></element:Shield> Beta Program Status",
          },
          hash: "8d9ce95b1e5257dfeb485e6553189c13",
        },
        "14/declaration/body/8/argument/11/1/3": {
          content: {
            en: "Your current beta program enrollment and progress",
          },
          hash: "8bd33b3d4efc0ba03629dc0451bb08d1",
        },
        "14/declaration/body/8/argument/11/3/1/1/1": {
          content: {
            en: "Finance Type",
          },
          hash: "7b3bc1b1a53cc69753b192c608ec0784",
        },
        "14/declaration/body/8/argument/11/3/1/3/1": {
          content: {
            en: "Onboarding Status",
          },
          hash: "19b00fadc7de768bb2c89ad241fc4036",
        },
        "14/declaration/body/8/argument/11/3/1/3/3/1/expression/alternate/1": {
          content: {
            en: "In Progress",
          },
          hash: "36e0792170e383e5f6cd1a2b3127e520",
        },
        "14/declaration/body/8/argument/11/3/1/3/3/1/expression/consequent/1": {
          content: {
            en: "Completed",
          },
          hash: "6004f678191486c2e839e0203e06448e",
        },
        "14/declaration/body/8/argument/11/3/1/5/1": {
          content: {
            en: "Beta Joined",
          },
          hash: "801df23295c3a73b4081dca23eb8ea1f",
        },
        "14/declaration/body/8/argument/11/3/1/7/1": {
          content: {
            en: "Feedback Submitted",
          },
          hash: "4626b6304e174ece8748316e7b99d877",
        },
        "14/declaration/body/8/argument/11/3/1/7/3": {
          content: {
            en: "<expression/> submissions",
          },
          hash: "9e54b81de5d616b903eea2bd93f9bb91",
        },
        "14/declaration/body/8/argument/15/1/1": {
          content: {
            en: "<element:RotateCcw></element:RotateCcw> Onboarding Management",
          },
          hash: "5d1fab6cc12c0638dff81855b716ffea",
        },
        "14/declaration/body/8/argument/15/1/3": {
          content: {
            en: "Reset your onboarding to try different finance types or start over",
          },
          hash: "6cc68c4c63ccb2b1a94bcdcf39463a6e",
        },
        "14/declaration/body/8/argument/15/3/1/3/1": {
          content: {
            en: "Reset Onboarding",
          },
          hash: "9742212daf8dc74538cef5c5486992f5",
        },
        "14/declaration/body/8/argument/15/3/1/3/3": {
          content: {
            en: "This will reset your onboarding progress and allow you to choose a different finance type. Your existing data will be preserved.",
          },
          hash: "cd54d197b0a37de589153cf4efd0af18",
        },
        "14/declaration/body/8/argument/15/3/1/3/5/3/1/1": {
          content: {
            en: "Reset Onboarding?",
          },
          hash: "46e5c3ce868a36319c7735d126273dcf",
        },
        "14/declaration/body/8/argument/15/3/1/3/5/3/1/3": {
          content: {
            en: "This will reset your onboarding progress and return you to the finance type selection. Your existing financial data will not be affected. You can choose a different finance type and go through the setup process again.",
          },
          hash: "966c3624dd97f9d15abfcbc28d01e6b2",
        },
        "14/declaration/body/8/argument/15/3/1/3/5/3/3/1": {
          content: {
            en: "Cancel",
          },
          hash: "2e2a849c2223911717de8caa2c71bade",
        },
        "14/declaration/body/8/argument/19/1/1": {
          content: {
            en: "<element:Settings></element:Settings> Data Management",
          },
          hash: "dc6489192a78620bd59ffef98d90133c",
        },
        "14/declaration/body/8/argument/19/1/3": {
          content: {
            en: "Export or reset your beta program data",
          },
          hash: "45ae8a8e28d005203514446893e2b217",
        },
        "14/declaration/body/8/argument/19/3/1/3/3/1": {
          content: {
            en: "Export Beta Data",
          },
          hash: "65bb4ff254f41a0f47173ef4e68d26ae",
        },
        "14/declaration/body/8/argument/19/3/1/3/3/3": {
          content: {
            en: "Download all your beta program data as a JSON file for backup or analysis.",
          },
          hash: "a81c11e99e289788d976cccc28972c3b",
        },
        "14/declaration/body/8/argument/19/3/1/3/3/5": {
          content: {
            en: "<element:Download></element:Download> Export Data",
          },
          hash: "99138537368f41e5e8c81cfe1ca7f3ba",
        },
        "14/declaration/body/8/argument/19/3/1/7/3/1": {
          content: {
            en: "Reset All Beta Data",
          },
          hash: "bc78d0f399fd0429184a9d11813e4c2f",
        },
        "14/declaration/body/8/argument/19/3/1/7/3/3": {
          content: {
            en: "Completely reset all beta program data. This cannot be undone.",
          },
          hash: "0b0513ba0571f623953750c4e9a93377",
        },
        "14/declaration/body/8/argument/19/3/1/7/3/5/3/1/1": {
          content: {
            en: "Reset All Beta Data?",
          },
          hash: "5aad3fa9af727b04ce6dec617327f13c",
        },
        "14/declaration/body/8/argument/19/3/1/7/3/5/3/1/3": {
          content: {
            en: "This will permanently delete all your beta program data including:<element:br></element:br>• Onboarding progress and preferences<element:br></element:br>• Beta-specific settings<element:br></element:br>• Feedback submissions<element:br></element:br>• Finance type selection<element:br></element:br><element:br></element:br><element:strong>This action cannot be undone.</element:strong> You will need to rejoin the beta program.",
          },
          hash: "d2efe0aca3728f12e639d593f84dbf5a",
        },
        "14/declaration/body/8/argument/19/3/1/7/3/5/3/3/1": {
          content: {
            en: "Cancel",
          },
          hash: "2e2a849c2223911717de8caa2c71bade",
        },
        "14/declaration/body/8/argument/23/expression/right/1/1": {
          content: {
            en: "Debug Information",
          },
          hash: "7a7892379e31868abba9865d20be2b72",
        },
        "14/declaration/body/8/argument/23/expression/right/1/3": {
          content: {
            en: "Development mode debug information",
          },
          hash: "109f726b6243904ba0e77be108403858",
        },
        "14/declaration/body/8/argument/23/expression/right/3/1/1/1": {
          content: {
            en: "User ID:",
          },
          hash: "2a9f2805f2c980ad59cb4c69c4c8c3da",
        },
        "14/declaration/body/8/argument/23/expression/right/3/1/3/1": {
          content: {
            en: "Finance Type:",
          },
          hash: "13167f6118af3cd41ccca5dec3df3b4e",
        },
        "14/declaration/body/8/argument/23/expression/right/3/1/5/1": {
          content: {
            en: "Onboarding Completed:",
          },
          hash: "29461e00e4a4ebc85d7efec4cbe037db",
        },
        "14/declaration/body/8/argument/23/expression/right/3/1/7/1": {
          content: {
            en: "Beta User:",
          },
          hash: "762bafea0e919d49c7908535ed2569e3",
        },
        "14/declaration/body/8/argument/23/expression/right/3/1/9/1": {
          content: {
            en: "LocalStorage Keys:",
          },
          hash: "c8271ab04fe17ced8cb7aa21b354480b",
        },
        "14/declaration/body/8/argument/3/1/1": {
          content: {
            en: "Beta Settings",
          },
          hash: "e03c429de86b0b3bd52e3886ab1d96f5",
        },
        "14/declaration/body/8/argument/3/1/3": {
          content: {
            en: "Manage your beta program settings and preferences",
          },
          hash: "78a93c7098d163dde9f223fc887fe549",
        },
        "14/declaration/body/8/argument/3/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> Beta Program",
          },
          hash: "b58b64b0c9892964dbd1b486f378b0ee",
        },
        "14/declaration/body/8/argument/7/1/1": {
          content: {
            en: "<element:Settings></element:Settings> Data Source Configuration",
          },
          hash: "f2ffc38a822a44c4b29d4be5f076838a",
        },
        "14/declaration/body/8/argument/7/1/3": {
          content: {
            en: "Switch between mock data for testing and real database for production use",
          },
          hash: "ebd7e587af2c1f2e149313cf1b145d73",
        },
      },
    },
    "app/beta/dashboard/smart-budgets/page.tsx": {
      entries: {
        "7/declaration/body/7/argument/11/1/1": {
          content: {
            en: "<element:Brain></element:Brain> AI Budget Recommendations",
          },
          hash: "08505d552a8bf1239e1e49fc51a8bb52",
        },
        "7/declaration/body/7/argument/11/1/3": {
          content: {
            en: "Personalized budget adjustments based on your spending patterns",
          },
          hash: "40fc56a4f166fd9a95e1c10d28fcb6e8",
        },
        "7/declaration/body/7/argument/11/3/1/1/expression/0/body/1/1/11/1/3/1":
          {
            content: {
              en: "AI Analysis",
            },
            hash: "703f40f9fdb1f4f05a4f48a755e8c297",
          },
        "7/declaration/body/7/argument/11/3/1/1/expression/0/body/1/1/15/1": {
          content: {
            en: "Apply Recommendation",
          },
          hash: "fb32179dc28c9df4f8d23192163ecfe9",
        },
        "7/declaration/body/7/argument/11/3/1/1/expression/0/body/1/1/15/3": {
          content: {
            en: "Learn More",
          },
          hash: "e0b524dab5722e2e1f1db8f0d67a55e8",
        },
        "7/declaration/body/7/argument/11/3/1/1/expression/0/body/1/1/15/5": {
          content: {
            en: "Dismiss",
          },
          hash: "5a8cb23ddfa90ba7176d96ad520a0b5f",
        },
        "7/declaration/body/7/argument/11/3/1/1/expression/0/body/1/1/3/3/1": {
          content: {
            en: "<element:Brain></element:Brain>{budget.confidence}% confidence",
          },
          hash: "7da21b76cc376baa9e60b5fe89d43da7",
        },
        "7/declaration/body/7/argument/11/3/1/1/expression/0/body/1/1/7/1/1": {
          content: {
            en: "Current Budget",
          },
          hash: "58d40219bcfa7bb2c1e6cf140b9ebbf3",
        },
        "7/declaration/body/7/argument/11/3/1/1/expression/0/body/1/1/7/1/5/3":
          {
            content: {
              en: "<function:formatCurrency/> spent (<function:getProgressPercentage/> %)",
            },
            hash: "53859bc4e4ceeb46dbf8ea1369f94189",
          },
        "7/declaration/body/7/argument/11/3/1/1/expression/0/body/1/1/7/3/1": {
          content: {
            en: "Smart Recommendation",
          },
          hash: "87d2d8b5205b33425bc9727a58298a42",
        },
        "7/declaration/body/7/argument/11/3/1/1/expression/0/body/1/1/7/3/5/3":
          {
            content: {
              en: "<function:formatCurrency/> spent (<function:getProgressPercentage/> %)",
            },
            hash: "53859bc4e4ceeb46dbf8ea1369f94189",
          },
        "7/declaration/body/7/argument/11/3/1/1/expression/0/body/1/1/7/5/1": {
          content: {
            en: "Impact",
          },
          hash: "9686af7abb7dcdf007370cce3c983024",
        },
        "7/declaration/body/7/argument/11/3/1/1/expression/0/body/1/1/7/5/5": {
          content: {
            en: "Expected outcome",
          },
          hash: "2b551162038595d5bed0fed61e8a2ea5",
        },
        "7/declaration/body/7/argument/15/1/1/3/1": {
          content: {
            en: "Smart Budget Technology",
          },
          hash: "958a1c54edab07bc9352c605a4501d0f",
        },
        "7/declaration/body/7/argument/15/1/1/3/3": {
          content: {
            en: "Our AI analyzes your spending history, seasonal patterns, and lifestyle changes to suggest optimal budget allocations that align with your actual needs and financial goals.",
          },
          hash: "34acc97eef2b775038f1272a10ee37f7",
        },
        "7/declaration/body/7/argument/15/1/1/3/5/1/1": {
          content: {
            en: "Predictive Modeling",
          },
          hash: "791ecdc5270daa74275752032ef1667b",
        },
        "7/declaration/body/7/argument/15/1/1/3/5/1/3": {
          content: {
            en: "Forecasts future spending based on historical patterns and trends",
          },
          hash: "69c75740984b504755135634d5702e29",
        },
        "7/declaration/body/7/argument/15/1/1/3/5/3/1": {
          content: {
            en: "Adaptive Learning",
          },
          hash: "c6310764de9b46d27fca75565a69b483",
        },
        "7/declaration/body/7/argument/15/1/1/3/5/3/3": {
          content: {
            en: "Continuously improves recommendations as it learns your habits",
          },
          hash: "9e05e0ed4d7452699f20a1f8b1d456dd",
        },
        "7/declaration/body/7/argument/15/1/1/3/5/5/1": {
          content: {
            en: "Seasonal Adjustments",
          },
          hash: "ea543cbbc881c70519536775875a84bc",
        },
        "7/declaration/body/7/argument/15/1/1/3/5/5/3": {
          content: {
            en: "Accounts for seasonal variations and life events automatically",
          },
          hash: "a62855ccc1fabc569cfc6098ae52a41d",
        },
        "7/declaration/body/7/argument/3/1/1/1": {
          content: {
            en: "Smart Budgets",
          },
          hash: "11bfedb33ff1ae6baa13fe80000208a1",
        },
        "7/declaration/body/7/argument/3/1/1/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> Beta Feature",
          },
          hash: "09dc542fabfbdc8021771ca5764ae80a",
        },
        "7/declaration/body/7/argument/3/1/3": {
          content: {
            en: "AI-powered budget optimization based on your spending patterns",
          },
          hash: "d3a3d0e45a7f627ac944ff453abba041",
        },
        "7/declaration/body/7/argument/3/3/1": {
          content: {
            en: "<element:Settings></element:Settings> Configure",
          },
          hash: "8128cc93f39cc808359492d988e88db7",
        },
        "7/declaration/body/7/argument/3/3/3": {
          content: {
            en: "<element:RefreshCw></element:RefreshCw> Refresh Analysis",
          },
          hash: "55c4657366af2228b7eaf854a749cfd1",
        },
        "7/declaration/body/7/argument/7/1/1/1": {
          content: {
            en: "<element:PieChart></element:PieChart> Current Budget",
          },
          hash: "edcf7280efef7ed223e7efda509e1238",
        },
        "7/declaration/body/7/argument/7/3/1/1": {
          content: {
            en: "<element:Brain></element:Brain> Smart Budget",
          },
          hash: "2da2edb36b86e48de71beee61d639f05",
        },
        "7/declaration/body/7/argument/7/5/1/1": {
          content: {
            en: "<element:Target></element:Target> Potential Savings",
          },
          hash: "6ed58b08477e7c8441607445166b52b6",
        },
        "7/declaration/body/7/argument/7/5/3/3": {
          content: {
            en: "Per month",
          },
          hash: "44cbbb767bc832fbd7f6c03f1383d8c2",
        },
        "7/declaration/body/7/argument/7/7/1/1": {
          content: {
            en: "<element:Zap></element:Zap> Recommendations",
          },
          hash: "f836edf6e3cad0e18378f5a9f09dc2ca",
        },
        "7/declaration/body/7/argument/7/7/3/3": {
          content: {
            en: "Active suggestions",
          },
          hash: "93674b195870641381234a9c00af4102",
        },
      },
    },
    "app/beta/dashboard/unified/page.tsx": {
      entries: {
        "9/declaration/body/10/argument/11/1/1/1": {
          content: {
            en: "Total Combined Balance",
          },
          hash: "126e8ff099149664ce10efb77e47895d",
        },
        "9/declaration/body/10/argument/11/1/3/3/1/expression/right": {
          content: {
            en: "Personal: <function:formatCurrency/>",
          },
          hash: "90031921214ea797c1ac799fbbcda07a",
        },
        "9/declaration/body/10/argument/11/1/3/3/3/expression/right": {
          content: {
            en: "Family: <function:formatCurrency/>",
          },
          hash: "0fb739dfc9ac19e555e48e7fba6cb8c8",
        },
        "9/declaration/body/10/argument/11/3/1/1": {
          content: {
            en: "Monthly Income",
          },
          hash: "f0d770bdda6bc1e8847686d1c8ac8997",
        },
        "9/declaration/body/10/argument/11/3/3/3/3": {
          content: {
            en: "+8.5% vs last month",
          },
          hash: "8b9a578ba4d7eac2e2c5a7308d870642",
        },
        "9/declaration/body/10/argument/11/5/1/1": {
          content: {
            en: "Monthly Expenses",
          },
          hash: "cfd957ffdc837c0851bdc307a220f692",
        },
        "9/declaration/body/10/argument/11/5/3/3/3": {
          content: {
            en: "+5.2% vs last month",
          },
          hash: "a86f4a5a64f6333e78f12c49b318a057",
        },
        "9/declaration/body/10/argument/11/7/1/1": {
          content: {
            en: "Savings Rate",
          },
          hash: "32bbceface3004ce15d7fc932f366230",
        },
        "9/declaration/body/10/argument/11/7/3/1": {
          content: {
            en: "<function:toFixed/> %",
          },
          hash: "9ca6a5d2340b8092d1b6f486ed4b8f8e",
        },
        "9/declaration/body/10/argument/11/7/3/3/3": {
          content: {
            en: "+2.1% vs last month",
          },
          hash: "ee1746b5490c7d00d5e607c011a91b3f",
        },
        "9/declaration/body/10/argument/15/1/1": {
          content: {
            en: "Overview",
          },
          hash: "30c54e4dc4ce599b87d94be34a8617f5",
        },
        "9/declaration/body/10/argument/15/1/3": {
          content: {
            en: "Trends",
          },
          hash: "90d281f05269f691670c78658097c7ae",
        },
        "9/declaration/body/10/argument/15/1/5": {
          content: {
            en: "Categories",
          },
          hash: "fd4e44f3b1b2bba9ca45f3aef963d042",
        },
        "9/declaration/body/10/argument/15/1/7": {
          content: {
            en: "Goals",
          },
          hash: "b20a0ce3273406177885d8b1f53f4bad",
        },
        "9/declaration/body/10/argument/15/3/1/3/1/1": {
          content: {
            en: "<element:BarChart3></element:BarChart3> Cash Flow Trends",
          },
          hash: "f0e7016ecfb5cbdc67d1d0d15fb5465d",
        },
        "9/declaration/body/10/argument/15/3/1/3/1/3": {
          content: {
            en: "Income vs expenses over the last 6 months",
          },
          hash: "cf90e30b3c93be5e55eaf463b06ecfc3",
        },
        "9/declaration/body/10/argument/15/3/1/3/3/1/1/expression/0/body/1/3": {
          content: {
            en: "Net: <function:formatCurrency/>",
          },
          hash: "c8a35edc701eca2ffafc82d24ba39fe6",
        },
        "9/declaration/body/10/argument/15/3/1/3/3/1/1/expression/0/body/3/1/3":
          {
            content: {
              en: "Income: <function:formatCurrency/>",
            },
            hash: "ee1a2fb1e5d03790ab5b20c1a70ec884",
          },
        "9/declaration/body/10/argument/15/3/1/3/3/1/1/expression/0/body/3/3/3":
          {
            content: {
              en: "Expenses: <function:formatCurrency/>",
            },
            hash: "8583e569bd18836f66fb85031f983040",
          },
        "9/declaration/body/10/argument/15/3/1/7/1/1": {
          content: {
            en: "<element:PieChart></element:PieChart> Balance Distribution",
          },
          hash: "eeee286614d0c8572ec95680c145689c",
        },
        "9/declaration/body/10/argument/15/3/1/7/1/3": {
          content: {
            en: "How your money is distributed across accounts",
          },
          hash: "d8b98f244517aceddde703deb8e58d06",
        },
        "9/declaration/body/10/argument/15/3/1/7/3/1/1/expression/right/1/1": {
          content: {
            en: "<element:User></element:User> Personal Accounts",
          },
          hash: "ee6911692ea61e61691e1e7399b903f2",
        },
        "9/declaration/body/10/argument/15/3/1/7/3/1/3/expression/right/1/1": {
          content: {
            en: "<element:Users></element:Users> Family Accounts",
          },
          hash: "72dfbcecb4b23a28abea77d05c026606",
        },
        "9/declaration/body/10/argument/15/5/1/1/1": {
          content: {
            en: "<element:PieChart></element:PieChart> Spending by Category",
          },
          hash: "a064a508a558721658c473373992dff6",
        },
        "9/declaration/body/10/argument/15/5/1/1/3": {
          content: {
            en: "Breakdown of expenses across different categories",
          },
          hash: "0df66ea4e38aa25ee3b67f1b5b916a2d",
        },
        "9/declaration/body/10/argument/15/5/1/3/1/1/expression/0/body/3/1/expression/right/1/1":
          {
            content: {
              en: "<element:User></element:User> Personal",
            },
            hash: "4d0ab2ffab1e32063890082eb7908629",
          },
        "9/declaration/body/10/argument/15/5/1/3/1/1/expression/0/body/3/3/expression/right/1/1":
          {
            content: {
              en: "<element:Users></element:Users> Family",
            },
            hash: "7d9b3466aee2b0a644889465efb5ecc4",
          },
        "9/declaration/body/10/argument/15/7/1/1/1": {
          content: {
            en: "<element:TrendingUp></element:TrendingUp> Financial Trends Analysis",
          },
          hash: "94dfa2138c93f4d835f9aafcfbefcc7a",
        },
        "9/declaration/body/10/argument/15/7/1/1/3": {
          content: {
            en: "Detailed analysis of your financial patterns over time",
          },
          hash: "9d4647148d1fca7a664ebd7bee3d118f",
        },
        "9/declaration/body/10/argument/15/7/1/3/1/3": {
          content: {
            en: "Advanced trend analysis charts would be displayed here",
          },
          hash: "03a6bc41bcd8bdafd77dae1a29f42d53",
        },
        "9/declaration/body/10/argument/15/7/1/3/1/5": {
          content: {
            en: "Integration with charting library (recharts) coming soon",
          },
          hash: "db90a371c7d59559481357721cf13793",
        },
        "9/declaration/body/10/argument/15/9/1/1/1": {
          content: {
            en: "<element:Target></element:Target> Goal Progress Analytics",
          },
          hash: "5bccd423bf757c90607e560495105a2c",
        },
        "9/declaration/body/10/argument/15/9/1/1/3": {
          content: {
            en: "Track and analyze progress on your financial goals",
          },
          hash: "a918d658641e6953f8bf61afc9c8efde",
        },
        "9/declaration/body/10/argument/15/9/1/3/1/3": {
          content: {
            en: "Goal analytics and forecasting would be displayed here",
          },
          hash: "88a0c2b5d784ec4a9881961ea463df3a",
        },
        "9/declaration/body/10/argument/15/9/1/3/1/5": {
          content: {
            en: "Real goal data integration coming soon",
          },
          hash: "5d488b4673616aeb36e7eb85f6d0602c",
        },
        "9/declaration/body/10/argument/19/1/1/3/1": {
          content: {
            en: "Advanced Analytics & Insights",
          },
          hash: "72b2a7f8cc00d728c9b2a3a4b1358544",
        },
        "9/declaration/body/10/argument/19/1/1/3/3": {
          content: {
            en: "This unified analytics view provides deep insights into your financial patterns, combining personal and family data for comprehensive analysis. Use the controls above to filter and customize your view for specific insights.",
          },
          hash: "928c001e06e2d7198b6c60696ce33d81",
        },
        "9/declaration/body/10/argument/19/1/1/3/5/1/1": {
          content: {
            en: "🔍 Pattern Recognition",
          },
          hash: "1aa6d21c5e7c5470defecc6732ad7651",
        },
        "9/declaration/body/10/argument/19/1/1/3/5/1/3": {
          content: {
            en: "Identify spending patterns and financial trends across time periods",
          },
          hash: "fcd1037c25e464cf56ac6f45cf6723c0",
        },
        "9/declaration/body/10/argument/19/1/1/3/5/3/1": {
          content: {
            en: "📊 Predictive Modeling",
          },
          hash: "dfd3da981e7aa27a433c37a1ae62e1de",
        },
        "9/declaration/body/10/argument/19/1/1/3/5/3/3": {
          content: {
            en: "Forecast future financial outcomes based on current trends",
          },
          hash: "ffc5904f4323ee699d0f6697aa0853c6",
        },
        "9/declaration/body/10/argument/3/1/1": {
          content: {
            en: "Unified Financial Analytics",
          },
          hash: "d213177f8097e3e1acdbde71916e469a",
        },
        "9/declaration/body/10/argument/3/1/3": {
          content: {
            en: "Advanced analytics and insights across your complete financial picture",
          },
          hash: "9322ddf31a0a7cf29e7d55a3e2312fff",
        },
        "9/declaration/body/10/argument/3/3/1": {
          content: {
            en: "<element:Filter></element:Filter> Filters",
          },
          hash: "3460391272d87095bebccd42fe4004d6",
        },
        "9/declaration/body/10/argument/3/3/3": {
          content: {
            en: "<element:Download></element:Download> Export",
          },
          hash: "82c7fb0a52965a8cde48deeee9cd663b",
        },
        "9/declaration/body/10/argument/7/1/1": {
          content: {
            en: "<element:BarChart3></element:BarChart3> Analytics Controls",
          },
          hash: "81f57d02c2f06b50633cd9925fea2e4f",
        },
        "9/declaration/body/10/argument/7/1/3": {
          content: {
            en: "Customize your analytics view and data sources",
          },
          hash: "c721c07b7cda6e7057531ba574b0f0b1",
        },
        "9/declaration/body/10/argument/7/3/1/1/1": {
          content: {
            en: "Time Range:",
          },
          hash: "333e420793085177d1ca536b61aa73ea",
        },
        "9/declaration/body/10/argument/7/3/1/3/1": {
          content: {
            en: "Data Sources:",
          },
          hash: "67d0d3a92c4e33e78776e1ea0fcf3396",
        },
        "9/declaration/body/10/argument/7/3/1/3/3/3": {
          content: {
            en: "<element:User></element:User> Personal",
          },
          hash: "1f40e73e41fe797453b4494655ed66bf",
        },
        "9/declaration/body/10/argument/7/3/1/3/5/3": {
          content: {
            en: "<element:Users></element:Users> Family",
          },
          hash: "c7e545a47d4ef60a487709eef30bb54f",
        },
      },
    },
    "app/beta/onboarding/page.tsx": {
      entries: {
        "6/declaration/body/5/consequent/0/argument/1/3": {
          content: {
            en: "Loading your beta account...",
          },
          hash: "438c12ab0c3d3f5bcbf1b3be46a3c093",
        },
      },
    },
    "app/beta/page.tsx": {
      entries: {
        "12/declaration/body/8/argument/11/1/1/1": {
          content: {
            en: "Exclusive Beta Features",
          },
          hash: "c6ad617ef57a967749a253a8813e703b",
        },
        "12/declaration/body/8/argument/11/1/1/3": {
          content: {
            en: "Get early access to revolutionary features that will transform your financial management experience.",
          },
          hash: "f6f7e0fe5acf8c78f3b1bb565ec64c4f",
        },
        "12/declaration/body/8/argument/15/1/1/1": {
          content: {
            en: "What Beta Users Say",
          },
          hash: "aacbc2f898e7d2d4962caba8b3bf4fcc",
        },
        "12/declaration/body/8/argument/15/1/1/3": {
          content: {
            en: "Real feedback from our amazing beta community",
          },
          hash: "9df347a977108f702027ee75f9d6d528",
        },
        "12/declaration/body/8/argument/15/1/3/1/expression/0/body/3/1": {
          content: {
            en: '"{testimonial.content}"',
          },
          hash: "227ff3b6be0965c8fca97512ca054870",
        },
        "12/declaration/body/8/argument/19/1/1/1": {
          content: {
            en: "Ready to Shape the Future?",
          },
          hash: "432dfdcc2cc982434c8f53741942f732",
        },
        "12/declaration/body/8/argument/19/1/1/3": {
          content: {
            en: "Join hundreds of users who are already experiencing the next generation of financial management. Your feedback directly influences our product development.",
          },
          hash: "c1717ed4d0b508d02d899763c579ec49",
        },
        "12/declaration/body/8/argument/19/1/1/5/expression/alternate/1/1-placeholder":
          {
            content: {
              en: "Enter your email",
            },
            hash: "39931962707c99b99a5a073ab579396b",
          },
        "12/declaration/body/8/argument/19/1/1/5/expression/consequent/1": {
          content: {
            en: "You're all set! Access your beta dashboard to get started.",
          },
          hash: "c32343965a6b42e2b067ba1b9bf11781",
        },
        "12/declaration/body/8/argument/19/1/1/5/expression/consequent/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> Go to Beta Dashboard<element:ArrowRight></element:ArrowRight>",
          },
          hash: "1db37388df6eded72475523437d76211",
        },
        "12/declaration/body/8/argument/19/1/1/7/3": {
          content: {
            en: "Questions? Email <NAME_EMAIL>",
          },
          hash: "3055a2cfbdb4a9cd85ca159024aab43e",
        },
        "12/declaration/body/8/argument/23/1/1/1/1/1": {
          content: {
            en: "BT",
          },
          hash: "b11b5c1a2d5674b439048448bf1db3a5",
        },
        "12/declaration/body/8/argument/23/1/1/1/3": {
          content: {
            en: "Budget Tracker Beta",
          },
          hash: "1be69ddc68a8036afa56607eecfd1971",
        },
        "12/declaration/body/8/argument/23/1/1/3/1": {
          content: {
            en: "Privacy",
          },
          hash: "28d3ca945c696e746bbd18f61dee2ea6",
        },
        "12/declaration/body/8/argument/23/1/1/3/3": {
          content: {
            en: "Terms",
          },
          hash: "7c343c4b6da35cdf97a7e3433d89d9a9",
        },
        "12/declaration/body/8/argument/23/1/1/3/5": {
          content: {
            en: "Contact",
          },
          hash: "1a313e3d08f61e2319817d8c23010e9c",
        },
        "12/declaration/body/8/argument/23/1/1/3/7": {
          content: {
            en: "<element:ArrowLeft></element:ArrowLeft> Main App",
          },
          hash: "6780d5261d101cd2290b3c6581504fd9",
        },
        "12/declaration/body/8/argument/3/1/1/1/1/1": {
          content: {
            en: "BT",
          },
          hash: "b11b5c1a2d5674b439048448bf1db3a5",
        },
        "12/declaration/body/8/argument/3/1/1/1/3": {
          content: {
            en: "Budget Tracker",
          },
          hash: "c970d03c09b541a19ea49ba831c210ee",
        },
        "12/declaration/body/8/argument/3/1/1/1/5": {
          content: {
            en: "<element:Sparkles></element:Sparkles> BETA",
          },
          hash: "8153f4c4ad64ceed0f3b67751ce23be0",
        },
        "12/declaration/body/8/argument/3/1/1/3/1": {
          content: {
            en: "<element:ArrowLeft></element:ArrowLeft> Main App",
          },
          hash: "6780d5261d101cd2290b3c6581504fd9",
        },
        "12/declaration/body/8/argument/3/1/1/3/3/expression/alternate/1/1": {
          content: {
            en: "<element:LogIn></element:LogIn> Beta Login",
          },
          hash: "210029026b51f10d2244bb121391b7e7",
        },
        "12/declaration/body/8/argument/3/1/1/3/3/expression/alternate/3/1": {
          content: {
            en: "<element:UserPlus></element:UserPlus> Join Beta",
          },
          hash: "29d6fa65e81768b2a074251e5d71aa24",
        },
        "12/declaration/body/8/argument/3/1/1/3/3/expression/consequent/1": {
          content: {
            en: "Welcome back!",
          },
          hash: "ca851bc73d4e01ba1fdce4e5f6bad21a",
        },
        "12/declaration/body/8/argument/3/1/1/3/3/expression/consequent/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> Access Beta",
          },
          hash: "27ed886633f7d0b864a606a090532b64",
        },
        "12/declaration/body/8/argument/7/1/1/1/3": {
          content: {
            en: "Exclusive Beta Access",
          },
          hash: "0a1b9af0d0a6712b9f2a6e2021b32eda",
        },
        "12/declaration/body/8/argument/7/1/1/3": {
          content: {
            en: "The Future of Financial Management",
          },
          hash: "fad16beb7c1092e9052f5257fe2f9443",
        },
        "12/declaration/body/8/argument/7/1/1/5": {
          content: {
            en: "Join our exclusive beta program and experience cutting-edge financial tools designed for modern families and individuals. Shape the future with your feedback.",
          },
          hash: "a22b2d03a8031f5609b9a24e56475d11",
        },
        "12/declaration/body/8/argument/7/1/1/7/expression/alternate/1/1-placeholder":
          {
            content: {
              en: "Enter your email",
            },
            hash: "39931962707c99b99a5a073ab579396b",
          },
        "12/declaration/body/8/argument/7/1/1/7/expression/consequent/1/1/3": {
          content: {
            en: "You're already signed in!",
          },
          hash: "3e8b8dd90835562911e00f6e1bbdf2af",
        },
        "12/declaration/body/8/argument/7/1/1/7/expression/consequent/1/3": {
          content: {
            en: "Ready to explore the beta features? Access your dashboard now.",
          },
          hash: "5605df4fbd0718f8c5a7a6b10dec9f2d",
        },
        "12/declaration/body/8/argument/7/1/1/7/expression/consequent/3": {
          content: {
            en: "<element:Sparkles></element:Sparkles> Access Beta Dashboard<element:ArrowRight></element:ArrowRight>",
          },
          hash: "bd3493568ba1dfc6f1bebe2a67abea16",
        },
        "12/declaration/body/8/argument/7/1/1/9/1": {
          content: {
            en: "<element:CheckCircle></element:CheckCircle> Free during beta",
          },
          hash: "19c55adb484bf93c69cfa9a1f3be1afa",
        },
        "12/declaration/body/8/argument/7/1/1/9/3": {
          content: {
            en: "<element:CheckCircle></element:CheckCircle> Early access features",
          },
          hash: "8b39af6a3cbc32b2308cf37ccd87ada1",
        },
        "12/declaration/body/8/argument/7/1/1/9/5": {
          content: {
            en: "<element:CheckCircle></element:CheckCircle> Direct feedback channel",
          },
          hash: "2f52165d0f5eb6b422574e9947613df8",
        },
      },
    },
    "app/beta/test/page.tsx": {
      entries: {
        "8/declaration/body/9/argument/11/expression/right/1": {
          content: {
            en: "Test Results",
          },
          hash: "5cbbdc89317390ce69ae988d3969e1c0",
        },
        "8/declaration/body/9/argument/11/expression/right/3/expression/0/body/3/expression/right/1/1":
          {
            content: {
              en: "View Details",
            },
            hash: "4b2a04eb8cfc6096cf6f3bb52ba75041",
          },
        "8/declaration/body/9/argument/15/1/1": {
          content: {
            en: "Before Running Tests",
          },
          hash: "20e72fa40611c005c3f077d4ca89675b",
        },
        "8/declaration/body/9/argument/15/3/1/1": {
          content: {
            en: "1. Database Setup",
          },
          hash: "90de6e37db754fdf86e41f87828667da",
        },
        "8/declaration/body/9/argument/15/3/1/3": {
          content: {
            en: "Make sure your Supabase project is active and the beta schema migration has been applied. See <element:code>src/beta/migrations/README.md</element:code> for instructions.",
          },
          hash: "826dd97e74a9ee740ff618606de61f0f",
        },
        "8/declaration/body/9/argument/15/3/3/1": {
          content: {
            en: "2. Environment Variables",
          },
          hash: "fc1acd525b6b025a1c16d1d7846fb588",
        },
        "8/declaration/body/9/argument/15/3/3/3": {
          content: {
            en: "Ensure your <element:code>.env.local</element:code> file has the correct Supabase credentials.",
          },
          hash: "7c8f08b46c72649c4d18980a10599365",
        },
        "8/declaration/body/9/argument/15/3/5/1": {
          content: {
            en: "3. Authentication",
          },
          hash: "f23585a57bebc329d90888ce9555ca9c",
        },
        "8/declaration/body/9/argument/15/3/5/3": {
          content: {
            en: "Sign in to test user-specific features like profile updates and feedback submission.",
          },
          hash: "4fe316776dcb8dbedfac305dba6bad13",
        },
        "8/declaration/body/9/argument/19/1/1": {
          content: {
            en: "What These Tests Check",
          },
          hash: "e57ded927403452af51fc0909175a19b",
        },
        "8/declaration/body/9/argument/19/3/1/1/1/3": {
          content: {
            en: "Database Connection",
          },
          hash: "f4933eaaa0cba3fc9179c374da4b7556",
        },
        "8/declaration/body/9/argument/19/3/1/1/3": {
          content: {
            en: "Verifies connection to Supabase database",
          },
          hash: "4d58ffa759908eed32f3f7853c096504",
        },
        "8/declaration/body/9/argument/19/3/1/3/1/3": {
          content: {
            en: "Beta Schema",
          },
          hash: "44eb8abcf0e0585603123cc8925e26eb",
        },
        "8/declaration/body/9/argument/19/3/1/3/3": {
          content: {
            en: "Checks if beta tables and fields exist",
          },
          hash: "96e21c5297719b44d063dd3e2aaac9d5",
        },
        "8/declaration/body/9/argument/19/3/1/5/1/3": {
          content: {
            en: "User Authentication",
          },
          hash: "a27bc7b99876b8d5c8c9e866cd7e9b0b",
        },
        "8/declaration/body/9/argument/19/3/1/5/3": {
          content: {
            en: "Tests user authentication and profile access",
          },
          hash: "809f13f88aa1c4967a6e3b0dc89a7b93",
        },
        "8/declaration/body/9/argument/19/3/1/7/1/3": {
          content: {
            en: "Beta Services",
          },
          hash: "df4a0b52f35385f82ca5bc21f20ab31c",
        },
        "8/declaration/body/9/argument/19/3/1/7/3": {
          content: {
            en: "Tests beta user operations and feedback",
          },
          hash: "c05e080d2efa958f3ea133e9519d78c6",
        },
        "8/declaration/body/9/argument/3/1/3": {
          content: {
            en: "Beta Integration Tests",
          },
          hash: "8a8239d46d1351feb4872fa33a20ccc8",
        },
        "8/declaration/body/9/argument/3/1/5": {
          content: {
            en: "BETA",
          },
          hash: "474612a2ec11624bb0da14bdc7aeeac3",
        },
        "8/declaration/body/9/argument/7/1/1": {
          content: {
            en: "Integration Test Suite",
          },
          hash: "1bc1cb9e268e312e412403648686aee6",
        },
        "8/declaration/body/9/argument/7/1/3": {
          content: {
            en: "Verify that the beta program backend integration is working correctly. This will test database connectivity, schema readiness, and service functionality.",
          },
          hash: "c05f3a9029255cdf7fb0638b1dd1a52f",
        },
        "8/declaration/body/9/argument/7/3/3/expression/right/1/1": {
          content: {
            en: "Running tests...",
          },
          hash: "fc949f6af7797c5dfbd3130922a21514",
        },
        "8/declaration/body/9/argument/7/3/3/expression/right/1/3": {
          content: {
            en: "{progress}%",
          },
          hash: "5ffe4a4526d680df3dc6d6e7880cbc9a",
        },
        "8/declaration/body/9/argument/7/3/7/expression/right/1/3": {
          content: {
            en: "{passedTests}/{totalTests} tests passed",
          },
          hash: "3a22059090b1df5c3accfc61a314e8e7",
        },
      },
    },
    "app/dashboard/accounts/page.tsx": {
      entries: {
        "22/declaration/body/25/argument/1/3/1/1": {
          content: {
            en: "<element:Plus></element:Plus> Add Account",
          },
          hash: "33af2711bf94a0565e37883fc7e11923",
        },
        "22/declaration/body/25/argument/1/3/3/3/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Bank Name",
            },
            hash: "325c10da30e56107173f140c9e955f23",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/1/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select a bank",
            },
            hash: "4bbba9921fa537354e23b454a37723ee",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/1/openingElement/2/value/expression/body/3/3/3":
          {
            content: {
              en: "Other",
            },
            hash: "79acaa6cd481262bea4e743a422529d2",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/11/openingElement/2/value/expression/body/3/1":
          {
            content: {
              en: "Active Account",
            },
            hash: "3782e32ce64e169e66b470425617b987",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/11/openingElement/2/value/expression/body/3/3":
          {
            content: {
              en: "Uncheck to hide this account from dashboards and reports",
            },
            hash: "3418ac9235c8acf9be0345e8a756042d",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/13/1": {
          content: {
            en: "<expression/> Account",
          },
          hash: "49ac044c9875d5360787bf9e699effe5",
        },
        "22/declaration/body/25/argument/1/3/3/3/1/3/expression/right/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Custom Bank Name",
            },
            hash: "a1be6a2484aee911b5a4f94cd900b1d2",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/3/expression/right/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "e.g., My Local Bank",
            },
            hash: "fd02d4eef1a7b70ec5ba219f9707dca7",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Account Type",
            },
            hash: "b6efe5b2f09f87606b6433e10f429f64",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select account type",
            },
            hash: "8657793038eaeeaf40fbc8ac4e332a16",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/3/1":
          {
            content: {
              en: "Checking",
            },
            hash: "2a47f296f2470d3f95bc9baf5bcace6c",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/3/11":
          {
            content: {
              en: "Other",
            },
            hash: "79acaa6cd481262bea4e743a422529d2",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/3/3":
          {
            content: {
              en: "Savings",
            },
            hash: "d4188fd5e78ab7e1c1f199e3c6d81387",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/3/5":
          {
            content: {
              en: "Credit Card",
            },
            hash: "9e50cdcdf12e214f1cd8190f5c676fd0",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/3/7":
          {
            content: {
              en: "Cash",
            },
            hash: "3dab2e9b7513729d38324a5d8ad95a3d",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/3/9":
          {
            content: {
              en: "Investment",
            },
            hash: "4534f4cbefcb6d49b4352c772fdcf942",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Initial Balance",
            },
            hash: "537b7f12f196cc60faabf7a56bf6b710",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "0.00",
            },
            hash: "5d9d7db256e581afdc5ab7e5196b009e",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Enter the current balance for accurate tracking",
            },
            hash: "ca03b1f16fa115304162af113f64957c",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/9/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Currency",
            },
            hash: "d2ba9a9cea418ea653e3ca6785617a3f",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/9/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select currency",
            },
            hash: "a7604892fc4bfe50ead2ba5d5dd95c9c",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/9/openingElement/2/value/expression/body/3/3/1":
          {
            content: {
              en: "USD ($)",
            },
            hash: "70d998854c94afcefc169b57e0b880cc",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/9/openingElement/2/value/expression/body/3/3/3":
          {
            content: {
              en: "EUR (€)",
            },
            hash: "b6e2713de3d3bd0f30508582bb5e82df",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/9/openingElement/2/value/expression/body/3/3/5":
          {
            content: {
              en: "GBP (£)",
            },
            hash: "f972658cf8cff55ad6c55801e1bb7bef",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/9/openingElement/2/value/expression/body/3/3/7":
          {
            content: {
              en: "JPY (¥)",
            },
            hash: "92e310366597441706d9a62811db554e",
          },
        "22/declaration/body/25/argument/1/3/3/3/1/9/openingElement/2/value/expression/body/3/3/9":
          {
            content: {
              en: "CAD ($)",
            },
            hash: "e76b742cccb277d4580ab4bc3a4a135d",
          },
        "22/declaration/body/25/argument/13/expression/alternate/1/expression/0/body/1/argument/1/1/3/1/1/expression/alternate":
          {
            content: {
              en: "<element:XCircle></element:XCircle> Inactive",
            },
            hash: "442b1c4f417331f49676245b0770c359",
          },
        "22/declaration/body/25/argument/13/expression/alternate/1/expression/0/body/1/argument/1/1/3/1/1/expression/consequent":
          {
            content: {
              en: "<element:CheckCircle2></element:CheckCircle2> Active",
            },
            hash: "484c1a226d4b921345492d60353ad9e6",
          },
        "22/declaration/body/25/argument/13/expression/consequent/1/3": {
          content: {
            en: "No accounts yet",
          },
          hash: "b2354d1c14b40b79bc612d00ef3be89c",
        },
        "22/declaration/body/25/argument/13/expression/consequent/1/5": {
          content: {
            en: "Add your first account to start tracking your finances",
          },
          hash: "32d7531653644607e5542c8895f15288",
        },
        "22/declaration/body/25/argument/13/expression/consequent/1/7": {
          content: {
            en: "<element:Plus></element:Plus> Add Account",
          },
          hash: "33af2711bf94a0565e37883fc7e11923",
        },
        "22/declaration/body/25/argument/5/expression/right/1": {
          content: {
            en: "<element:Bell></element:Bell> Account Alerts",
          },
          hash: "776042ec9f2c2c8e6e2caf9a21f71014",
        },
        "22/declaration/body/25/argument/5/expression/right/3/1/expression/0/body/1/argument/1/1/3/3":
          {
            content: {
              en: "<element:Edit></element:Edit> Edit",
            },
            hash: "82449403ba23e100a26b20cf48605364",
          },
        "22/declaration/body/25/argument/9/1/3/1": {
          content: {
            en: "Total Net Worth",
          },
          hash: "c48a4da235c3f171ed3a9239dffc78c2",
        },
        "22/declaration/body/25/argument/9/1/5/3": {
          content: {
            en: "Assets minus liabilities",
          },
          hash: "759836568aa24f2b40242c45bb290dc9",
        },
        "22/declaration/body/25/argument/9/3/3/1": {
          content: {
            en: "Active Accounts",
          },
          hash: "f426590ab6f20436ad50024155fbb2f7",
        },
        "22/declaration/body/25/argument/9/3/5/3": {
          content: {
            en: "Currently active accounts",
          },
          hash: "4175a829d2bdec19875bc376090269cf",
        },
        "22/declaration/body/25/argument/9/5/3/1": {
          content: {
            en: "Total Assets",
          },
          hash: "63b7f2a516d939c1bbecaa26843da7e8",
        },
        "22/declaration/body/25/argument/9/5/5/3": {
          content: {
            en: "Excluding credit card debt",
          },
          hash: "cc789acee82ad617e8fe7b65ea03af9b",
        },
        "22/declaration/body/25/argument/9/7/3/1": {
          content: {
            en: "Credit Card Debt",
          },
          hash: "c80d29c47a6816ed83b531721d2dd5e2",
        },
        "22/declaration/body/25/argument/9/7/5/3": {
          content: {
            en: "Total outstanding on credit cards",
          },
          hash: "2dea39571bb5e9942a50808a8fedf79a",
        },
      },
    },
    "app/dashboard/analytics/page.tsx": {
      entries: {
        "17/declaration/body/18/argument/1/3/1/3/1": {
          content: {
            en: "Last Month",
          },
          hash: "9ab119dff0cb00d094cef2066572fe92",
        },
        "17/declaration/body/18/argument/1/3/1/3/3": {
          content: {
            en: "Last 3 Months",
          },
          hash: "9fe9b397bd8f84a4337c93fef22a7325",
        },
        "17/declaration/body/18/argument/1/3/1/3/5": {
          content: {
            en: "Last 6 Months",
          },
          hash: "40074a0c15af6811bb0688292a2b5e1f",
        },
        "17/declaration/body/18/argument/1/3/1/3/7": {
          content: {
            en: "Last Year",
          },
          hash: "42547108d452358d115c59f8e663e0a5",
        },
        "17/declaration/body/18/argument/1/3/1/3/9": {
          content: {
            en: "All Time",
          },
          hash: "0c74212d6e146e76143cdf2c13a6ad16",
        },
        "17/declaration/body/18/argument/13/1/1": {
          content: {
            en: "<element:LineChartIcon></element:LineChartIcon> Trends",
          },
          hash: "91bac3104ad35553e3f6112f36c146ca",
        },
        "17/declaration/body/18/argument/13/1/3": {
          content: {
            en: "<element:Activity></element:Activity> Daily",
          },
          hash: "d769ccd23e86284b416cbb1c0b4aac22",
        },
        "17/declaration/body/18/argument/13/1/5": {
          content: {
            en: "<element:BarChartIcon></element:BarChartIcon> Monthly",
          },
          hash: "d72f4b97fa2d396eef334cfb420bb083",
        },
        "17/declaration/body/18/argument/13/1/7": {
          content: {
            en: "<element:PieChartIcon></element:PieChartIcon> Categories",
          },
          hash: "497dc803fcec73cfa002618d7a8bd42b",
        },
        "17/declaration/body/18/argument/13/3/1/1/1": {
          content: {
            en: "Income & Expense Trends",
          },
          hash: "4708a2ec5f7b3797fc178b2f89bc6b41",
        },
        "17/declaration/body/18/argument/13/3/1/1/3": {
          content: {
            en: "Track your income and expenses over time",
          },
          hash: "35d887dba2a28b086a51ae75f18f4ad6",
        },
        "17/declaration/body/18/argument/13/5/1/1/1": {
          content: {
            en: "Daily Spending Patterns",
          },
          hash: "1d2ef98e40dbd3c7b39dcb225c0b88e7",
        },
        "17/declaration/body/18/argument/13/5/1/1/3": {
          content: {
            en: "Analyze your daily financial activity over the last 30 days",
          },
          hash: "62bdabd34a6c80674363d724c7a09877",
        },
        "17/declaration/body/18/argument/13/7/1/1/1": {
          content: {
            en: "Monthly Comparison",
          },
          hash: "4a48857e5860ecae993109821bd151eb",
        },
        "17/declaration/body/18/argument/13/7/1/1/3": {
          content: {
            en: "Compare income and expenses by month",
          },
          hash: "8cb0b53dd390d7bac11b409c0264f777",
        },
        "17/declaration/body/18/argument/13/9/1/1/1": {
          content: {
            en: "Expense Categories",
          },
          hash: "e41ba23bec29e9168e24c903e93ebaf9",
        },
        "17/declaration/body/18/argument/13/9/1/1/3": {
          content: {
            en: "Breakdown of your expenses by category",
          },
          hash: "1a8a21760f085e1ac2689d6e4b4d7a33",
        },
        "17/declaration/body/18/argument/13/9/1/3/1/3/1": {
          content: {
            en: "Category Breakdown",
          },
          hash: "0133a10f11eec562a550d0bf9b07c982",
        },
        "17/declaration/body/18/argument/5/1/3/1": {
          content: {
            en: "Total Income",
          },
          hash: "d96c55cd509ed55f6dfbf78dbcf0605d",
        },
        "17/declaration/body/18/argument/5/3/3/1": {
          content: {
            en: "Total Expenses",
          },
          hash: "57bfc5bd4e97342446e0423f6a9c0fd0",
        },
        "17/declaration/body/18/argument/5/5/3/1": {
          content: {
            en: "Net Income",
          },
          hash: "6a1b190c2d9f5e15bfc3a32c0be286a0",
        },
        "17/declaration/body/18/argument/5/5/5/3": {
          content: {
            en: "Income - Expenses",
          },
          hash: "a6b8db7f3cffa8389531be47a210e720",
        },
        "17/declaration/body/18/argument/5/7/3/1": {
          content: {
            en: "Savings Rate",
          },
          hash: "32bbceface3004ce15d7fc932f366230",
        },
        "17/declaration/body/18/argument/5/7/5/1": {
          content: {
            en: "<function:totals.savingsRate.toFixed/>%",
          },
          hash: "f0b7f727bc805a82ffd8099388cd415d",
        },
        "17/declaration/body/18/argument/5/7/5/3": {
          content: {
            en: "Of total income",
          },
          hash: "e054bb00b57ac83a9e0916fabb9aef1b",
        },
        "17/declaration/body/18/argument/9/1/3/1": {
          content: {
            en: "Daily Spending",
          },
          hash: "128d09a4449adfb415c4b361554187c5",
        },
        "17/declaration/body/18/argument/9/1/5/3": {
          content: {
            en: "Average per day<expression/><expression/>",
          },
          hash: "35c5afd5b7694facd7bb1d985e494568",
        },
        "17/declaration/body/18/argument/9/3/3/1": {
          content: {
            en: "Monthly Projection",
          },
          hash: "2c164be20f070370f8f5c9d3aef20ad1",
        },
        "17/declaration/body/18/argument/9/3/5/3": {
          content: {
            en: "Based on current pace",
          },
          hash: "74f147c8e9740bd70d2969eb6264919e",
        },
        "17/declaration/body/18/argument/9/5/3/1": {
          content: {
            en: "Financial Health",
          },
          hash: "ca411e758eafd9feba6a9b9deb6f16a6",
        },
        "17/declaration/body/18/argument/9/5/5/3": {
          content: {
            en: "<function:totals.savingsRate.toFixed/>% savings rate",
          },
          hash: "f795fb17af49d3784a95069a60e071df",
        },
      },
    },
    "app/dashboard/budgets/page.tsx": {
      entries: {
        "23/declaration/body/25/argument/1/3/1/1": {
          content: {
            en: "<element:Plus></element:Plus> New Budget",
          },
          hash: "540a59659ad17c7175b19a43859d6317",
        },
        "23/declaration/body/25/argument/1/3/3/1/3": {
          content: {
            en: "Set a spending limit for a specific category or overall",
          },
          hash: "e691da3a8f1f6a1c3b7b9791b58b6716",
        },
        "23/declaration/body/25/argument/1/3/3/3/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Budget Name",
            },
            hash: "8e6aa9f369873bd34c06b45213fbaf7f",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "e.g., Groceries Budget",
            },
            hash: "9d6ab61c799be3157251ca8f15c552c3",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/11/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Start Date",
            },
            hash: "1331b3b439f4d711cb03d4b1be92d480",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/13/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "End Date (Optional)",
            },
            hash: "399c5d6c7132a9196275b74e4347e303",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/13/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Leave blank for recurring budgets",
            },
            hash: "400c30c3280b14638719d789f546e1e8",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/15/1": {
          content: {
            en: "<expression/> Budget",
          },
          hash: "06f35dc8ab25ad52be13472e053e7471",
        },
        "23/declaration/body/25/argument/1/3/3/3/1/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Budget Amount",
            },
            hash: "aa13b3e97684589cec0d11cd198893fb",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/3/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "0.00",
            },
            hash: "5d9d7db256e581afdc5ab7e5196b009e",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Currency",
            },
            hash: "d2ba9a9cea418ea653e3ca6785617a3f",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select currency",
            },
            hash: "a7604892fc4bfe50ead2ba5d5dd95c9c",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/3/1":
          {
            content: {
              en: "USD ($)",
            },
            hash: "70d998854c94afcefc169b57e0b880cc",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/3/3":
          {
            content: {
              en: "EUR (€)",
            },
            hash: "b6e2713de3d3bd0f30508582bb5e82df",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/3/5":
          {
            content: {
              en: "GBP (£)",
            },
            hash: "f972658cf8cff55ad6c55801e1bb7bef",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/3/7":
          {
            content: {
              en: "JPY (¥)",
            },
            hash: "92e310366597441706d9a62811db554e",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/3/9":
          {
            content: {
              en: "CAD ($)",
            },
            hash: "e76b742cccb277d4580ab4bc3a4a135d",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Budget Period",
            },
            hash: "3581853cc8d83fd629cbc72de48f152f",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select period",
            },
            hash: "f3e492e15b52807eda6248fcdd65e36f",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/3/1":
          {
            content: {
              en: "Weekly",
            },
            hash: "9e687411dbf1248496a4ae5dd5391679",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/3/3":
          {
            content: {
              en: "Monthly",
            },
            hash: "818f1192e32bb855597f930d3e78806e",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/3/5":
          {
            content: {
              en: "Quarterly",
            },
            hash: "ab6c67fa292e1aab3222c67a786465e7",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/3/7":
          {
            content: {
              en: "Yearly",
            },
            hash: "87f43e016c19cb25860f456549a2f431",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/3/9":
          {
            content: {
              en: "One-time",
            },
            hash: "0bd69cc0cc7d7510480b15569886e65a",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/9/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Category (Optional)",
            },
            hash: "bff671a5e11b4d8e264ba8deef37d8ee",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/9/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "All Categories",
            },
            hash: "2547e684d62a4710bdfbb9415a9970d0",
          },
        "23/declaration/body/25/argument/1/3/3/3/1/9/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Leave blank to track all spending",
            },
            hash: "e51938eb79ff36d57304a63a967a4010",
          },
        "23/declaration/body/25/argument/13/expression/alternate/1/expression/0/body/1/argument/1/1/1/3/3/expression/right":
          {
            content: {
              en: "• {budget.category_name}",
            },
            hash: "99ccb4acb838f40b4b4d613bee966170",
          },
        "23/declaration/body/25/argument/13/expression/alternate/1/expression/0/body/1/argument/3/1/1":
          {
            content: {
              en: "<function:formatCurrency/> of <function:formatCurrency/>",
            },
            hash: "f15662f3c501949394c98f6392578517",
          },
        "23/declaration/body/25/argument/13/expression/alternate/1/expression/0/body/1/argument/3/1/3/1/expression/alternate/alternate":
          {
            content: {
              en: "<element:TrendingUp></element:TrendingUp><function:toFixed/> % Used",
            },
            hash: "0050b12c19817359f8a60c384ae41e4b",
          },
        "23/declaration/body/25/argument/13/expression/alternate/1/expression/0/body/1/argument/3/1/3/1/expression/alternate/consequent":
          {
            content: {
              en: "<element:CheckCircle2></element:CheckCircle2> Not Started",
            },
            hash: "5e7e32cf91313c51be932f79a7ecd992",
          },
        "23/declaration/body/25/argument/13/expression/alternate/1/expression/0/body/1/argument/3/1/3/1/expression/consequent":
          {
            content: {
              en: "<element:AlertCircle></element:AlertCircle> Over Budget",
            },
            hash: "665f397918e944a77174e0ddc0bd7e4e",
          },
        "23/declaration/body/25/argument/13/expression/alternate/1/expression/0/body/1/argument/3/5/3/3/expression/right":
          {
            content: {
              en: "  - <function:Date.toLocaleDateString/>",
            },
            hash: "c43761dc3304f0767c4be9e2088b2fb3",
          },
        "23/declaration/body/25/argument/13/expression/consequent/1/3": {
          content: {
            en: "No budgets yet",
          },
          hash: "c36efdf4a4c460a854c861f7ce90ff84",
        },
        "23/declaration/body/25/argument/13/expression/consequent/1/5": {
          content: {
            en: "Create your first budget to start tracking your spending",
          },
          hash: "2fbbaab7ca6975b3fe3d6760f9ae3851",
        },
        "23/declaration/body/25/argument/13/expression/consequent/1/7": {
          content: {
            en: "<element:Plus></element:Plus> Create Budget",
          },
          hash: "d0ae62e41484c6075c23dd692faa8f0f",
        },
        "23/declaration/body/25/argument/5/expression/right/1": {
          content: {
            en: "<element:Bell></element:Bell> Budget Alerts",
          },
          hash: "56239f43390cead841a90689e3ef60ee",
        },
        "23/declaration/body/25/argument/5/expression/right/3/1/expression/0/body/1/argument/1/1/3":
          {
            content: {
              en: "<element:Edit></element:Edit> Edit",
            },
            hash: "b01b83d90c761f384953074a718d330b",
          },
        "23/declaration/body/25/argument/9/1/3/1": {
          content: {
            en: "Total Budgeted",
          },
          hash: "cf73357da34965a9a858cffff4e3150f",
        },
        "23/declaration/body/25/argument/9/1/5/3": {
          content: {
            en: "Across {budgetsToDisplay.length} budgets",
          },
          hash: "e03cda4420e6caa56f25f4096eef7d85",
        },
        "23/declaration/body/25/argument/9/3/3/1": {
          content: {
            en: "Total Spent",
          },
          hash: "006d95dd6507afe98601c460793c5158",
        },
        "23/declaration/body/25/argument/9/3/5/3": {
          content: {
            en: "<expression/> % of total budget",
          },
          hash: "f32af4fa9355a042bc488d0b6e9884db",
        },
        "23/declaration/body/25/argument/9/5/3/1": {
          content: {
            en: "Remaining",
          },
          hash: "5db6eb99c266450c802a2bd345e5dce0",
        },
        "23/declaration/body/25/argument/9/5/5/3": {
          content: {
            en: "Available to spend",
          },
          hash: "8cb923f89f68c2dc55eaa0b134a15705",
        },
        "23/declaration/body/25/argument/9/7/3/1": {
          content: {
            en: "On Track",
          },
          hash: "b1a84d2d1c659d6d61fd8665d17d4707",
        },
        "23/declaration/body/25/argument/9/7/5/3": {
          content: {
            en: "Budgets under 75% used",
          },
          hash: "07172302fae4b3b7b730b755025b7338",
        },
      },
    },
    "app/dashboard/categories/page.tsx": {
      entries: {
        "22/declaration/body/26/argument/1/3/1": {
          content: {
            en: "<element:Lightbulb></element:Lightbulb> Add Default Categories",
          },
          hash: "bff66d52dc454fac3acfd93262778675",
        },
        "22/declaration/body/26/argument/1/3/3/1/1": {
          content: {
            en: "<element:Plus></element:Plus> New Category",
          },
          hash: "c5bac0e9fca92fddf3eda02386eeb40f",
        },
        "22/declaration/body/26/argument/1/3/3/3/1/3": {
          content: {
            en: "Organize your transactions with custom categories",
          },
          hash: "dda44046a086a741a114ccb29b4ca5f0",
        },
        "22/declaration/body/26/argument/1/3/3/3/3/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Category Name",
            },
            hash: "af03e079cbe712ce34f241627e6bfe7e",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "e.g., Groceries",
            },
            hash: "3cfb178d39584faab40b9239234c6b70",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/11/1": {
          content: {
            en: "<expression/> Category",
          },
          hash: "ccff49020873493aaa165d6fa9cc3f2b",
        },
        "22/declaration/body/26/argument/1/3/3/3/3/1/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Type",
            },
            hash: "f04471a7ddac844b9ad145eb9911ef75",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/3/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select type",
            },
            hash: "fa373e47f55ff081982844a853be3a88",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/3/openingElement/2/value/expression/body/3/3/1":
          {
            content: {
              en: "Income",
            },
            hash: "398bff8476817f942c70c4aadad5dd91",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/3/openingElement/2/value/expression/body/3/3/3":
          {
            content: {
              en: "Expense",
            },
            hash: "5d71ac3af27428af3058019d1cbfcc61",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Color",
            },
            hash: "9d53d1d120e8b8954bcae9a322573748",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/5/openingElement/2/value/expression/body/5/1-placeholder":
          {
            content: {
              en: "#000000",
            },
            hash: "66fb001d4991ce98bc792a46d330233a",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/5/openingElement/2/value/expression/body/7":
          {
            content: {
              en: "Select a color or enter a custom hex code",
            },
            hash: "d64883a097f992b66d1554d0c276e013",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/7/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Parent Category (Optional)",
            },
            hash: "4775da4b194c375ce3dddd4d7fd18ac0",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/7/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "No parent category",
            },
            hash: "f95681f8757799f36e9206470921eb2e",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/7/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Optionally nest this under another category",
            },
            hash: "bd65205faff60b9f07877cc4e494341a",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/9/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Icon (Optional)",
            },
            hash: "2953bb925798c7b7f3a1a97c49b7c7a0",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/9/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "e.g., shopping-cart, home, car",
            },
            hash: "1c7b8eaf8138615942943a9d483fadcf",
          },
        "22/declaration/body/26/argument/1/3/3/3/3/1/9/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Use Lucide icon names like: shopping-cart, home, car, utensils, briefcase, etc.",
            },
            hash: "cb668b269cd68a768b69ee81573b7b29",
          },
        "22/declaration/body/26/argument/13/1": {
          content: {
            en: "All",
          },
          hash: "1e231f4b008d575c987c67c5529b36b9",
        },
        "22/declaration/body/26/argument/13/3": {
          content: {
            en: "<element:ArrowUpRight></element:ArrowUpRight> Income",
          },
          hash: "3ee4f92d21fbf5aef846334fe3c862bc",
        },
        "22/declaration/body/26/argument/13/5": {
          content: {
            en: "<element:ArrowDownRight></element:ArrowDownRight> Expense",
          },
          hash: "ec4f1304fb7822883dab9fbbf38432c2",
        },
        "22/declaration/body/26/argument/17/expression/alternate/1/expression/0/body/3/1/3/expression/right":
          {
            content: {
              en: "Sub-category",
            },
            hash: "93e0f3e319132e888a2a1bac1f7ed98a",
          },
        "22/declaration/body/26/argument/17/expression/alternate/1/expression/0/body/3/5/1/1":
          {
            content: {
              en: "Usage:",
            },
            hash: "9cde44d8242be26666aeb43dd203808f",
          },
        "22/declaration/body/26/argument/17/expression/alternate/1/expression/0/body/3/5/1/3":
          {
            content: {
              en: "<expression/> transactions",
            },
            hash: "232fb0ad5e158b575ed290ca576e7e80",
          },
        "22/declaration/body/26/argument/17/expression/alternate/1/expression/0/body/3/5/3/expression/right/1":
          {
            content: {
              en: "Total:",
            },
            hash: "f88aa2f74615d4c75984e36154b9a6e9",
          },
        "22/declaration/body/26/argument/17/expression/alternate/1/expression/0/body/3/5/3/expression/right/3":
          {
            content: {
              en: "$<function:toLocaleString/>",
            },
            hash: "b4f3f55fe07d6d285653dd87536a04a8",
          },
        "22/declaration/body/26/argument/17/expression/alternate/1/expression/0/body/3/5/5/expression/right":
          {
            content: {
              en: "<element:AlertCircle></element:AlertCircle> Unused category",
            },
            hash: "ebc409f2ae16abbf4a4b89bfd0890073",
          },
        "22/declaration/body/26/argument/17/expression/consequent/1/3": {
          content: {
            en: "No categories found",
          },
          hash: "a4e087b44399dadaa85b052df8ea4738",
        },
        "22/declaration/body/26/argument/17/expression/consequent/1/7": {
          content: {
            en: "<element:Plus></element:Plus> Create Category",
          },
          hash: "9f779a07d47fbd02e1e0aa5fb3c03aae",
        },
        "22/declaration/body/26/argument/5/1/3/1": {
          content: {
            en: "Total Categories",
          },
          hash: "c87fabbe725d765878acfc2dca28d784",
        },
        "22/declaration/body/26/argument/5/1/5/3": {
          content: {
            en: "{incomeCategories} income, {expenseCategories} expense",
          },
          hash: "7c01761a17b633fae66502664d743a0b",
        },
        "22/declaration/body/26/argument/5/3/3/1": {
          content: {
            en: "Income Categories",
          },
          hash: "a782a125898cfc95f38154375ee91048",
        },
        "22/declaration/body/26/argument/5/3/5/3": {
          content: {
            en: "Categories for tracking income",
          },
          hash: "3d1ca5b404df8d3f7b0788f225ad3212",
        },
        "22/declaration/body/26/argument/5/5/3/1": {
          content: {
            en: "Expense Categories",
          },
          hash: "a2d7b3eec9fb07ffa5689adbc9a74601",
        },
        "22/declaration/body/26/argument/5/5/5/3": {
          content: {
            en: "Categories for tracking expenses",
          },
          hash: "5bff079ece340286a0939c3ee6f1990d",
        },
        "22/declaration/body/26/argument/5/7/3/1": {
          content: {
            en: "Most Active",
          },
          hash: "dfbf2a65b7b91ab04ece2274c008b045",
        },
        "22/declaration/body/26/argument/5/9/3/1": {
          content: {
            en: "Unused Categories",
          },
          hash: "aa19cf58dba2072ad5292d5733bf079c",
        },
        "22/declaration/body/26/argument/5/9/5/3": {
          content: {
            en: "Categories with no transactions",
          },
          hash: "e1195b01f91b3cb4bbb55e616d7886f1",
        },
        "22/declaration/body/26/argument/9/expression/right/1/expression/right/1/1":
          {
            content: {
              en: "<element:TrendingUp></element:TrendingUp> Most Used Categories",
            },
            hash: "127ec3d7a259fa9c706fc42abef0b343",
          },
        "22/declaration/body/26/argument/9/expression/right/1/expression/right/3/1/expression/0/body/1/1/1":
          {
            content: {
              en: "#<expression/>",
            },
            hash: "fc4aac8e2e2237e2f2c76fc5e1dc6c7f",
          },
        "22/declaration/body/26/argument/9/expression/right/1/expression/right/3/1/expression/0/body/3/1":
          {
            content: {
              en: "{category.usage_count} uses",
            },
            hash: "9e0fe638ec24a2a0505e1c9883c36726",
          },
        "22/declaration/body/26/argument/9/expression/right/1/expression/right/3/1/expression/0/body/3/3":
          {
            content: {
              en: "$<function:toLocaleString/>",
            },
            hash: "b4f3f55fe07d6d285653dd87536a04a8",
          },
        "22/declaration/body/26/argument/9/expression/right/5/expression/right/1/1":
          {
            content: {
              en: "<element:Lightbulb></element:Lightbulb> Add More Categories",
            },
            hash: "9c44fe30fdfdf271e43e0a6139ac32b9",
          },
        "22/declaration/body/26/argument/9/expression/right/5/expression/right/3/1/5/expression/callee/body/1/consequent/0/argument/1":
          {
            content: {
              en: 'You have all available predefined categories! Create custom ones using the "New Category" button.',
            },
            hash: "7b3f442578f69f80679017872b42df96",
          },
        "22/declaration/body/26/argument/9/expression/right/5/expression/right/3/1/5/expression/callee/body/4/argument/1/expression/right/1":
          {
            content: {
              en: "Expense Categories",
            },
            hash: "069412ab2ac367dbeeeb83a6a52b274f",
          },
        "22/declaration/body/26/argument/9/expression/right/5/expression/right/3/1/5/expression/callee/body/4/argument/3/expression/right/1":
          {
            content: {
              en: "Income Categories",
            },
            hash: "387175dee3052c685a1b7deceee4f148",
          },
      },
    },
    "app/dashboard/family/[groupId]/accounts/page.tsx": {
      entries: {
        "27/declaration/body/17/consequent/0/argument/1/1/3": {
          content: {
            en: "Loading accounts...",
          },
          hash: "e5684d235d12405913850bfd9e53159d",
        },
        "27/declaration/body/18/consequent/0/argument/1/1/1/3": {
          content: {
            en: "Error Loading Accounts",
          },
          hash: "f12973ec3d5f453181f7e06772cd4f5d",
        },
        "27/declaration/body/19/argument/3/1/1/1": {
          content: {
            en: "<element:Home></element:Home> Family Finance",
          },
          hash: "79de5499e77a22740841354a18cdecb8",
        },
        "27/declaration/body/19/argument/3/1/1/3": {
          content: {
            en: "/",
          },
          hash: "d727054cb65d0e95c79cfb34bc3e3f28",
        },
        "27/declaration/body/19/argument/3/1/1/5": {
          content: {
            en: "Group Dashboard",
          },
          hash: "cf8794641808c11d9c19873ef61492f2",
        },
        "27/declaration/body/19/argument/3/1/1/7": {
          content: {
            en: "/",
          },
          hash: "d727054cb65d0e95c79cfb34bc3e3f28",
        },
        "27/declaration/body/19/argument/3/1/1/9": {
          content: {
            en: "Accounts",
          },
          hash: "dbb9796c495f5e0f0de6f71bd32527ae",
        },
        "27/declaration/body/19/argument/3/1/3": {
          content: {
            en: "Family Accounts",
          },
          hash: "50b8835fd4e35456d084f17b307c12b9",
        },
        "27/declaration/body/19/argument/3/1/5": {
          content: {
            en: "Manage your family group's bank accounts and financial assets",
          },
          hash: "a2e1552d33c7e5186b90aeb5420f6703",
        },
        "27/declaration/body/19/argument/3/3/1/1": {
          content: {
            en: "<element:Plus></element:Plus> Add Account",
          },
          hash: "33af2711bf94a0565e37883fc7e11923",
        },
        "27/declaration/body/19/argument/3/3/3/3/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Bank Name",
            },
            hash: "325c10da30e56107173f140c9e955f23",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/1/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select a bank",
            },
            hash: "4bbba9921fa537354e23b454a37723ee",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/3/expression/right/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Custom Bank Name",
            },
            hash: "a1be6a2484aee911b5a4f94cd900b1d2",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/3/expression/right/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "Enter bank name",
            },
            hash: "ffbd24766e5e6c83adf6fa4db8c6e4ce",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Account Type",
            },
            hash: "b6efe5b2f09f87606b6433e10f429f64",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select account type",
            },
            hash: "8657793038eaeeaf40fbc8ac4e332a16",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/3/1":
          {
            content: {
              en: "Checking",
            },
            hash: "2a47f296f2470d3f95bc9baf5bcace6c",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/3/11":
          {
            content: {
              en: "Other",
            },
            hash: "79acaa6cd481262bea4e743a422529d2",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/3/3":
          {
            content: {
              en: "Savings",
            },
            hash: "d4188fd5e78ab7e1c1f199e3c6d81387",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/3/5":
          {
            content: {
              en: "Credit Card",
            },
            hash: "9e50cdcdf12e214f1cd8190f5c676fd0",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/3/7":
          {
            content: {
              en: "Cash",
            },
            hash: "3dab2e9b7513729d38324a5d8ad95a3d",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/3/9":
          {
            content: {
              en: "Investment",
            },
            hash: "4534f4cbefcb6d49b4352c772fdcf942",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/7/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Current Balance",
            },
            hash: "a2e10ddd47b35e3100cbef74ec386cd0",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/7/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "0.00",
            },
            hash: "5d9d7db256e581afdc5ab7e5196b009e",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/7/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Currency",
            },
            hash: "d2ba9a9cea418ea653e3ca6785617a3f",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/7/3/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select currency",
            },
            hash: "a7604892fc4bfe50ead2ba5d5dd95c9c",
          },
        "27/declaration/body/19/argument/3/3/3/3/1/9/1": {
          content: {
            en: "Cancel",
          },
          hash: "643431b4a45271ffb02d2957be7884b3",
        },
        "27/declaration/body/19/argument/7/expression/alternate/1/3": {
          content: {
            en: "No accounts yet",
          },
          hash: "b2354d1c14b40b79bc612d00ef3be89c",
        },
        "27/declaration/body/19/argument/7/expression/alternate/1/5": {
          content: {
            en: "Start by adding your first family account to track your finances together.",
          },
          hash: "4b8a0a50020e503500415c71b44b8f44",
        },
        "27/declaration/body/19/argument/7/expression/alternate/1/7/1/1": {
          content: {
            en: "<element:Plus></element:Plus> Add First Account",
          },
          hash: "c500e3047b3d67469e8e33d9eb657102",
        },
        "27/declaration/body/19/argument/7/expression/consequent/1/expression/0/body/3/1/1/1":
          {
            content: {
              en: "Balance",
            },
            hash: "b2b27c7cb12c29dc34108302b42e03a3",
          },
        "27/declaration/body/19/argument/7/expression/consequent/1/expression/0/body/3/1/3/1":
          {
            content: {
              en: "Created by",
            },
            hash: "6775c2fa7d495fea48f1ad816daea93b",
          },
        "27/declaration/body/19/argument/7/expression/consequent/1/expression/0/body/3/1/5/1":
          {
            content: {
              en: "Created",
            },
            hash: "6045b4ff97fb7dca19ebadfa2e34a700",
          },
      },
    },
    "app/dashboard/family/[groupId]/budgets/page.tsx": {
      entries: {
        "27/declaration/body/20/consequent/0/argument/1/1/3": {
          content: {
            en: "Loading budgets...",
          },
          hash: "981ad15e3db0633bdf86baa8c79e0d78",
        },
        "27/declaration/body/21/consequent/0/argument/1/1/1/3": {
          content: {
            en: "Error Loading Budgets",
          },
          hash: "0eb0595e8c141fe9518eacae8b01f421",
        },
        "27/declaration/body/22/argument/3/1/1/1": {
          content: {
            en: "<element:Home></element:Home> Family Finance",
          },
          hash: "79de5499e77a22740841354a18cdecb8",
        },
        "27/declaration/body/22/argument/3/1/1/3": {
          content: {
            en: "/",
          },
          hash: "d727054cb65d0e95c79cfb34bc3e3f28",
        },
        "27/declaration/body/22/argument/3/1/1/5": {
          content: {
            en: "Group Dashboard",
          },
          hash: "cf8794641808c11d9c19873ef61492f2",
        },
        "27/declaration/body/22/argument/3/1/1/7": {
          content: {
            en: "/",
          },
          hash: "d727054cb65d0e95c79cfb34bc3e3f28",
        },
        "27/declaration/body/22/argument/3/1/1/9": {
          content: {
            en: "Budgets",
          },
          hash: "8dcb6cddfd7b9a0ce4a4e908b86662f7",
        },
        "27/declaration/body/22/argument/3/1/3": {
          content: {
            en: "Family Budgets",
          },
          hash: "f9ceb1c4f162ee704f4142efa87e18c9",
        },
        "27/declaration/body/22/argument/3/1/5": {
          content: {
            en: "Set and track spending limits for your family group",
          },
          hash: "61c7cf65f934a78e2d9bafce5a6e45e5",
        },
        "27/declaration/body/22/argument/3/3/1/1": {
          content: {
            en: "<element:Plus></element:Plus> Create Budget",
          },
          hash: "d0ae62e41484c6075c23dd692faa8f0f",
        },
        "27/declaration/body/22/argument/3/3/3/3/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Budget Name",
            },
            hash: "8e6aa9f369873bd34c06b45213fbaf7f",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "e.g., Groceries, Entertainment",
            },
            hash: "540688b2c339a695c927f21f07426b11",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/11/1": {
          content: {
            en: "Cancel",
          },
          hash: "643431b4a45271ffb02d2957be7884b3",
        },
        "27/declaration/body/22/argument/3/3/3/3/1/3/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Budget Amount",
            },
            hash: "aa13b3e97684589cec0d11cd198893fb",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/3/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "1000.00",
            },
            hash: "f896d134d72f3e862768142bb3dc3ae7",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/3/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Currency",
            },
            hash: "d2ba9a9cea418ea653e3ca6785617a3f",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/3/3/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select currency",
            },
            hash: "a7604892fc4bfe50ead2ba5d5dd95c9c",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Budget Period",
            },
            hash: "3581853cc8d83fd629cbc72de48f152f",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select period",
            },
            hash: "f3e492e15b52807eda6248fcdd65e36f",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/3/1":
          {
            content: {
              en: "Weekly",
            },
            hash: "9e687411dbf1248496a4ae5dd5391679",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/3/3":
          {
            content: {
              en: "Monthly",
            },
            hash: "818f1192e32bb855597f930d3e78806e",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/3/5":
          {
            content: {
              en: "Quarterly",
            },
            hash: "ab6c67fa292e1aab3222c67a786465e7",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/3/7":
          {
            content: {
              en: "Yearly",
            },
            hash: "87f43e016c19cb25860f456549a2f431",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/3/9":
          {
            content: {
              en: "One-time",
            },
            hash: "0bd69cc0cc7d7510480b15569886e65a",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/7/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Category (Optional)",
            },
            hash: "bff671a5e11b4d8e264ba8deef37d8ee",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/7/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select category",
            },
            hash: "48c701bd440d494da6a6fac2570e7196",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/7/openingElement/2/value/expression/body/3/3/1":
          {
            content: {
              en: "No Category",
            },
            hash: "0bc04793eabacb8d4352d74d05678947",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/9/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Start Date",
            },
            hash: "1331b3b439f4d711cb03d4b1be92d480",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/9/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "End Date (Optional)",
            },
            hash: "399c5d6c7132a9196275b74e4347e303",
          },
        "27/declaration/body/22/argument/3/3/3/3/1/9/3/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Leave empty for recurring budgets",
            },
            hash: "acb96750c21c70bca49f2c1880956946",
          },
        "27/declaration/body/22/argument/7/expression/alternate/1/3": {
          content: {
            en: "No budgets yet",
          },
          hash: "c36efdf4a4c460a854c861f7ce90ff84",
        },
        "27/declaration/body/22/argument/7/expression/alternate/1/5": {
          content: {
            en: "Create your first budget to start tracking your family's spending limits.",
          },
          hash: "fdc00b1619e1584392aae2dd3ccf676a",
        },
        "27/declaration/body/22/argument/7/expression/alternate/1/7/1/1": {
          content: {
            en: "<element:Plus></element:Plus> Create First Budget",
          },
          hash: "0986929e5055f9f00c64d9dee68eaf0d",
        },
        "27/declaration/body/22/argument/7/expression/consequent/1/expression/0/body/4/argument/1/1/3/3":
          {
            content: {
              en: "<function:budget.period.replace/> budget",
            },
            hash: "54a43b05c2b9337c49a4265da1352dab",
          },
        "27/declaration/body/22/argument/7/expression/consequent/1/expression/0/body/4/argument/1/3/1":
          {
            content: {
              en: "<function:percentage.toFixed/>% used",
            },
            hash: "a977fda96afb95cfd0c94024e6c09ef8",
          },
        "27/declaration/body/22/argument/7/expression/consequent/1/expression/0/body/4/argument/3/1/1/1/1/1":
          {
            content: {
              en: "Spent:",
            },
            hash: "06240acb105188f2b12fc82a6d57ab10",
          },
        "27/declaration/body/22/argument/7/expression/consequent/1/expression/0/body/4/argument/3/1/1/1/3/1":
          {
            content: {
              en: "Budget:",
            },
            hash: "f024625ae14839df749fc02d10d3e62c",
          },
        "27/declaration/body/22/argument/7/expression/consequent/1/expression/0/body/4/argument/3/1/1/1/5/1":
          {
            content: {
              en: "Remaining:",
            },
            hash: "5c4f2a1d6f56ea84746c6107122d8db7",
          },
        "27/declaration/body/22/argument/7/expression/consequent/1/expression/0/body/4/argument/3/1/5/1":
          {
            content: {
              en: "Created by {budget.created_by_email}",
            },
            hash: "ef5e60ebeb3347890a9feeba11c401de",
          },
      },
    },
    "app/dashboard/family/[groupId]/categories/page.tsx": {
      entries: {
        "25/declaration/body/31/argument/1/1/1/1": {
          content: {
            en: "<element:Home></element:Home> Family Finance",
          },
          hash: "79de5499e77a22740841354a18cdecb8",
        },
        "25/declaration/body/31/argument/1/1/1/3": {
          content: {
            en: "/",
          },
          hash: "d727054cb65d0e95c79cfb34bc3e3f28",
        },
        "25/declaration/body/31/argument/1/1/1/5": {
          content: {
            en: "Group Dashboard",
          },
          hash: "cf8794641808c11d9c19873ef61492f2",
        },
        "25/declaration/body/31/argument/1/1/1/7": {
          content: {
            en: "/",
          },
          hash: "d727054cb65d0e95c79cfb34bc3e3f28",
        },
        "25/declaration/body/31/argument/1/1/1/9": {
          content: {
            en: "Categories",
          },
          hash: "fd4e44f3b1b2bba9ca45f3aef963d042",
        },
        "25/declaration/body/31/argument/1/1/3": {
          content: {
            en: "Family Categories",
          },
          hash: "10dd727c33598b1b8effcffdd3062c5f",
        },
        "25/declaration/body/31/argument/1/1/5": {
          content: {
            en: "Organize your family's transactions with custom categories",
          },
          hash: "a4aa2851411a166efb3bede76ebd8f1d",
        },
        "25/declaration/body/31/argument/1/3/1": {
          content: {
            en: "<element:Lightbulb></element:Lightbulb> Add Default Categories",
          },
          hash: "bff66d52dc454fac3acfd93262778675",
        },
        "25/declaration/body/31/argument/1/3/3/1/1": {
          content: {
            en: "<element:Plus></element:Plus> New Category",
          },
          hash: "c5bac0e9fca92fddf3eda02386eeb40f",
        },
        "25/declaration/body/31/argument/1/3/3/3/1/3": {
          content: {
            en: "Organize your family transactions with custom categories",
          },
          hash: "fe602e3041c01bd9b69469d794d995ea",
        },
        "25/declaration/body/31/argument/1/3/3/3/3/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Category Name",
            },
            hash: "af03e079cbe712ce34f241627e6bfe7e",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "e.g., Groceries",
            },
            hash: "3cfb178d39584faab40b9239234c6b70",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/11/1": {
          content: {
            en: "<expression/> Category",
          },
          hash: "ccff49020873493aaa165d6fa9cc3f2b",
        },
        "25/declaration/body/31/argument/1/3/3/3/3/1/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Type",
            },
            hash: "f04471a7ddac844b9ad145eb9911ef75",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/3/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select type",
            },
            hash: "fa373e47f55ff081982844a853be3a88",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/3/openingElement/2/value/expression/body/3/3/1":
          {
            content: {
              en: "Income",
            },
            hash: "398bff8476817f942c70c4aadad5dd91",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/3/openingElement/2/value/expression/body/3/3/3":
          {
            content: {
              en: "Expense",
            },
            hash: "5d71ac3af27428af3058019d1cbfcc61",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Color",
            },
            hash: "9d53d1d120e8b8954bcae9a322573748",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/5/openingElement/2/value/expression/body/5/1-placeholder":
          {
            content: {
              en: "#000000",
            },
            hash: "66fb001d4991ce98bc792a46d330233a",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/5/openingElement/2/value/expression/body/7":
          {
            content: {
              en: "Select a color or enter a custom hex code",
            },
            hash: "d64883a097f992b66d1554d0c276e013",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/7/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Icon (Optional)",
            },
            hash: "2953bb925798c7b7f3a1a97c49b7c7a0",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/7/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "e.g., shopping-cart, home, car",
            },
            hash: "1c7b8eaf8138615942943a9d483fadcf",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/7/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Use Lucide icon names like: shopping-cart, home, car, utensils, briefcase, etc.",
            },
            hash: "cb668b269cd68a768b69ee81573b7b29",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/9/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Parent Category (Optional)",
            },
            hash: "4775da4b194c375ce3dddd4d7fd18ac0",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/9/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "No parent category",
            },
            hash: "f95681f8757799f36e9206470921eb2e",
          },
        "25/declaration/body/31/argument/1/3/3/3/3/1/9/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Optionally nest this under another category",
            },
            hash: "bd65205faff60b9f07877cc4e494341a",
          },
        "25/declaration/body/31/argument/13/1": {
          content: {
            en: "All",
          },
          hash: "1e231f4b008d575c987c67c5529b36b9",
        },
        "25/declaration/body/31/argument/13/3": {
          content: {
            en: "<element:ArrowUpRight></element:ArrowUpRight> Income",
          },
          hash: "3ee4f92d21fbf5aef846334fe3c862bc",
        },
        "25/declaration/body/31/argument/13/5": {
          content: {
            en: "<element:ArrowDownRight></element:ArrowDownRight> Expense",
          },
          hash: "ec4f1304fb7822883dab9fbbf38432c2",
        },
        "25/declaration/body/31/argument/17/expression/alternate/1/expression/0/body/3/1/3/expression/right":
          {
            content: {
              en: "Sub-category",
            },
            hash: "93e0f3e319132e888a2a1bac1f7ed98a",
          },
        "25/declaration/body/31/argument/17/expression/alternate/1/expression/0/body/3/5/1/1":
          {
            content: {
              en: "Usage:",
            },
            hash: "9cde44d8242be26666aeb43dd203808f",
          },
        "25/declaration/body/31/argument/17/expression/alternate/1/expression/0/body/3/5/1/3":
          {
            content: {
              en: "<expression/> transactions",
            },
            hash: "232fb0ad5e158b575ed290ca576e7e80",
          },
        "25/declaration/body/31/argument/17/expression/alternate/1/expression/0/body/3/5/3/expression/right/1":
          {
            content: {
              en: "Total:",
            },
            hash: "f88aa2f74615d4c75984e36154b9a6e9",
          },
        "25/declaration/body/31/argument/17/expression/alternate/1/expression/0/body/3/5/3/expression/right/3":
          {
            content: {
              en: "$<function:toLocaleString/>",
            },
            hash: "b4f3f55fe07d6d285653dd87536a04a8",
          },
        "25/declaration/body/31/argument/17/expression/alternate/1/expression/0/body/3/9/1":
          {
            content: {
              en: "Created by {category.created_by_email}",
            },
            hash: "ef5e60ebeb3347890a9feeba11c401de",
          },
        "25/declaration/body/31/argument/17/expression/consequent/1/3": {
          content: {
            en: "No categories found",
          },
          hash: "a4e087b44399dadaa85b052df8ea4738",
        },
        "25/declaration/body/31/argument/17/expression/consequent/1/7": {
          content: {
            en: "<element:Plus></element:Plus> Create Category",
          },
          hash: "9f779a07d47fbd02e1e0aa5fb3c03aae",
        },
        "25/declaration/body/31/argument/5/1/3/1": {
          content: {
            en: "Total Categories",
          },
          hash: "c87fabbe725d765878acfc2dca28d784",
        },
        "25/declaration/body/31/argument/5/1/5/3": {
          content: {
            en: "{incomeCategories} income, {expenseCategories} expense",
          },
          hash: "7c01761a17b633fae66502664d743a0b",
        },
        "25/declaration/body/31/argument/5/3/3/1": {
          content: {
            en: "Income Categories",
          },
          hash: "a782a125898cfc95f38154375ee91048",
        },
        "25/declaration/body/31/argument/5/3/5/3": {
          content: {
            en: "Categories for tracking income",
          },
          hash: "3d1ca5b404df8d3f7b0788f225ad3212",
        },
        "25/declaration/body/31/argument/5/5/3/1": {
          content: {
            en: "Expense Categories",
          },
          hash: "a2d7b3eec9fb07ffa5689adbc9a74601",
        },
        "25/declaration/body/31/argument/5/5/5/3": {
          content: {
            en: "Categories for tracking expenses",
          },
          hash: "5bff079ece340286a0939c3ee6f1990d",
        },
        "25/declaration/body/31/argument/5/7/3/1": {
          content: {
            en: "Most Active",
          },
          hash: "dfbf2a65b7b91ab04ece2274c008b045",
        },
        "25/declaration/body/31/argument/5/9/3/1": {
          content: {
            en: "Unused Categories",
          },
          hash: "aa19cf58dba2072ad5292d5733bf079c",
        },
        "25/declaration/body/31/argument/5/9/5/3": {
          content: {
            en: "Categories with no transactions",
          },
          hash: "e1195b01f91b3cb4bbb55e616d7886f1",
        },
        "25/declaration/body/31/argument/9/expression/right/1/expression/right/1/1":
          {
            content: {
              en: "<element:TrendingUp></element:TrendingUp> Most Used Categories",
            },
            hash: "127ec3d7a259fa9c706fc42abef0b343",
          },
        "25/declaration/body/31/argument/9/expression/right/1/expression/right/3/1/expression/0/body/1/1/1":
          {
            content: {
              en: "#<expression/>",
            },
            hash: "fc4aac8e2e2237e2f2c76fc5e1dc6c7f",
          },
        "25/declaration/body/31/argument/9/expression/right/1/expression/right/3/1/expression/0/body/3/1":
          {
            content: {
              en: "{category.usage_count} uses",
            },
            hash: "9e0fe638ec24a2a0505e1c9883c36726",
          },
        "25/declaration/body/31/argument/9/expression/right/1/expression/right/3/1/expression/0/body/3/3":
          {
            content: {
              en: "$<function:toLocaleString/>",
            },
            hash: "b4f3f55fe07d6d285653dd87536a04a8",
          },
        "25/declaration/body/31/argument/9/expression/right/5/expression/right/1/1":
          {
            content: {
              en: "<element:Lightbulb></element:Lightbulb> Add More Categories",
            },
            hash: "9c44fe30fdfdf271e43e0a6139ac32b9",
          },
        "25/declaration/body/31/argument/9/expression/right/5/expression/right/3/1/5/expression/callee/body/1/consequent/0/argument/1":
          {
            content: {
              en: 'You have all available predefined categories! Create custom ones using the "New Category" button.',
            },
            hash: "7b3f442578f69f80679017872b42df96",
          },
        "25/declaration/body/31/argument/9/expression/right/5/expression/right/3/1/5/expression/callee/body/4/argument/1/expression/right/1":
          {
            content: {
              en: "Expense Categories",
            },
            hash: "069412ab2ac367dbeeeb83a6a52b274f",
          },
        "25/declaration/body/31/argument/9/expression/right/5/expression/right/3/1/5/expression/callee/body/4/argument/3/expression/right/1":
          {
            content: {
              en: "Income Categories",
            },
            hash: "387175dee3052c685a1b7deceee4f148",
          },
      },
    },
    "app/dashboard/family/[groupId]/goals/page.tsx": {
      entries: {
        "27/declaration/body/21/0/init/body/0/consequent/0/argument": {
          content: {
            en: "<element:CheckCircle2></element:CheckCircle2> Completed",
          },
          hash: "3f4fbee459c7d03fb61dd7148e689116",
        },
        "27/declaration/body/21/0/init/body/1/consequent/0/alternate/consequent/0/argument":
          {
            content: {
              en: "<element:Clock></element:Clock> Urgent",
            },
            hash: "ccca0d84a7a8b84b3e257c0707534005",
          },
        "27/declaration/body/21/0/init/body/1/consequent/0/consequent/0/argument":
          {
            content: {
              en: "<element:AlertTriangle></element:AlertTriangle> Overdue",
            },
            hash: "957af395374c29d9638b46c6849add2f",
          },
        "27/declaration/body/21/0/init/body/2/argument": {
          content: {
            en: "<element:Target></element:Target> Active",
          },
          hash: "e3a2aadd0679de0e91557e8d7b7deeeb",
        },
        "27/declaration/body/22/consequent/0/argument/1/1/3": {
          content: {
            en: "Loading goals...",
          },
          hash: "3484dc1517fbb6b3af3dde667e5f9e9f",
        },
        "27/declaration/body/23/consequent/0/argument/1/1/1/3": {
          content: {
            en: "Error Loading Goals",
          },
          hash: "f46de0d58b8f6fea92d0fe92984cc61e",
        },
        "27/declaration/body/24/argument/3/1/1/1": {
          content: {
            en: "<element:Home></element:Home> Family Finance",
          },
          hash: "79de5499e77a22740841354a18cdecb8",
        },
        "27/declaration/body/24/argument/3/1/1/3": {
          content: {
            en: "/",
          },
          hash: "d727054cb65d0e95c79cfb34bc3e3f28",
        },
        "27/declaration/body/24/argument/3/1/1/5": {
          content: {
            en: "Group Dashboard",
          },
          hash: "cf8794641808c11d9c19873ef61492f2",
        },
        "27/declaration/body/24/argument/3/1/1/7": {
          content: {
            en: "/",
          },
          hash: "d727054cb65d0e95c79cfb34bc3e3f28",
        },
        "27/declaration/body/24/argument/3/1/1/9": {
          content: {
            en: "Goals",
          },
          hash: "b20a0ce3273406177885d8b1f53f4bad",
        },
        "27/declaration/body/24/argument/3/1/3": {
          content: {
            en: "Family Goals",
          },
          hash: "c8bfd0270baafb021794081a90ffe71a",
        },
        "27/declaration/body/24/argument/3/1/5": {
          content: {
            en: "Set and track financial goals for your family group",
          },
          hash: "0f748903194f5c4036ed89ceeefa1961",
        },
        "27/declaration/body/24/argument/3/3/1/1": {
          content: {
            en: "<element:Plus></element:Plus> Create Goal",
          },
          hash: "d92ecee0b0fa7ad0d1a0f941e304052d",
        },
        "27/declaration/body/24/argument/3/3/3/3/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Goal Name",
            },
            hash: "c2935a53240b1ded4f1072f5d505dda0",
          },
        "27/declaration/body/24/argument/3/3/3/3/1/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "e.g., Emergency Fund, Vacation",
            },
            hash: "a945d39fcd1fdd7e0a302a46fa07eed4",
          },
        "27/declaration/body/24/argument/3/3/3/3/1/11/1": {
          content: {
            en: "Cancel",
          },
          hash: "643431b4a45271ffb02d2957be7884b3",
        },
        "27/declaration/body/24/argument/3/3/3/3/1/3/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Target Amount",
            },
            hash: "4c5ef34bfffe9afe09c952876641741a",
          },
        "27/declaration/body/24/argument/3/3/3/3/1/3/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "10000.00",
            },
            hash: "3ea6586c60a3206a320da84e7d2f53e5",
          },
        "27/declaration/body/24/argument/3/3/3/3/1/3/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Current Amount",
            },
            hash: "1b3b5c106a2ce5a955a3f995d6a1c368",
          },
        "27/declaration/body/24/argument/3/3/3/3/1/3/3/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "0.00",
            },
            hash: "5d9d7db256e581afdc5ab7e5196b009e",
          },
        "27/declaration/body/24/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Currency",
            },
            hash: "d2ba9a9cea418ea653e3ca6785617a3f",
          },
        "27/declaration/body/24/argument/3/3/3/3/1/5/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select currency",
            },
            hash: "a7604892fc4bfe50ead2ba5d5dd95c9c",
          },
        "27/declaration/body/24/argument/3/3/3/3/1/7/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Target Date (Optional)",
            },
            hash: "3bd7facf1fb507000e19d963be331b86",
          },
        "27/declaration/body/24/argument/3/3/3/3/1/7/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Set a deadline to track progress more effectively",
            },
            hash: "56bfce9541d24a1542ea47cc47446e6e",
          },
        "27/declaration/body/24/argument/3/3/3/3/1/9/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Color",
            },
            hash: "9d53d1d120e8b8954bcae9a322573748",
          },
        "27/declaration/body/24/argument/7/expression/alternate/1/3": {
          content: {
            en: "No goals yet",
          },
          hash: "737010e6d019fd628888e70c69110e01",
        },
        "27/declaration/body/24/argument/7/expression/alternate/1/5": {
          content: {
            en: "Create your first financial goal to start tracking your family's savings progress.",
          },
          hash: "4622c6e1be71fc0cc5f66ae2484634c5",
        },
        "27/declaration/body/24/argument/7/expression/alternate/1/7/1/1": {
          content: {
            en: "<element:Plus></element:Plus> Create First Goal",
          },
          hash: "c115db0c116a81cb3e18cf610130a295",
        },
        "27/declaration/body/24/argument/7/expression/consequent/1/expression/0/body/3/1/1/1/1":
          {
            content: {
              en: "Progress",
            },
            hash: "dd0200d5849ebb7d64c15098ae91d229",
          },
        "27/declaration/body/24/argument/7/expression/consequent/1/expression/0/body/3/1/1/1/3":
          {
            content: {
              en: "<function:goal.progress.toFixed/>%",
            },
            hash: "815b69e2ba7c258c55d7390d6ff1fc1c",
          },
        "27/declaration/body/24/argument/7/expression/consequent/1/expression/0/body/3/1/3/1/1":
          {
            content: {
              en: "Current:",
            },
            hash: "b9588ca79dda729c36c45852fc2990a0",
          },
        "27/declaration/body/24/argument/7/expression/consequent/1/expression/0/body/3/1/3/3/1":
          {
            content: {
              en: "Target:",
            },
            hash: "b4ebf751c370bd835bbfad7a7a6b5dac",
          },
        "27/declaration/body/24/argument/7/expression/consequent/1/expression/0/body/3/1/3/5/1":
          {
            content: {
              en: "Remaining:",
            },
            hash: "11101f90c287c65dad9452b99dc921e5",
          },
        "27/declaration/body/24/argument/7/expression/consequent/1/expression/0/body/3/1/5/expression/right/1":
          {
            content: {
              en: "Save ~<function:formatCurrency/> /month to reach your goal",
            },
            hash: "28d894d0cd161484301d7a20459c3a01",
          },
        "27/declaration/body/24/argument/7/expression/consequent/1/expression/0/body/3/1/7/1":
          {
            content: {
              en: "Created by {goal.created_by_email}",
            },
            hash: "ef5e60ebeb3347890a9feeba11c401de",
          },
        "27/declaration/body/24/argument/7/expression/consequent/1/expression/0/body/3/1/7/3/expression/right":
          {
            content: {
              en: "Due <function:Date.toLocaleDateString/>",
            },
            hash: "fb58ad9c20ea602b438b110ed909eba3",
          },
      },
    },
    "app/dashboard/family/[groupId]/page.tsx": {
      entries: {
        "19/declaration/body/15/consequent/0/argument/3": {
          content: {
            en: "Loading family finance dashboard...",
          },
          hash: "7da9941f609067335c1f92a58c18cd9b",
        },
        "19/declaration/body/16/consequent/1/argument/1/3": {
          content: {
            en: "Error Loading Group",
          },
          hash: "92fc7f2763e4218039b7d55316cd3c39",
        },
        "19/declaration/body/16/consequent/1/argument/1/5": {
          content: {
            en: "{errorMessage}<element:br></element:br> Please check the group ID or ensure you are a member of this group.",
          },
          hash: "f3b5b629410233a04bde4020bd971d3c",
        },
        "19/declaration/body/17/argument/11/expression/right/1/1/1": {
          content: {
            en: "Additional Metrics",
          },
          hash: "05b37a49ec9ea48ad87362b89aed114a",
        },
        "19/declaration/body/17/argument/11/expression/right/1/1/3": {
          content: {
            en: "Financial health indicators",
          },
          hash: "2d6fbb6070df9771eec8ffda6253089c",
        },
        "19/declaration/body/17/argument/11/expression/right/1/3/1/1/1/1": {
          content: {
            en: "Net Cash Flow",
          },
          hash: "821ffc060c7a8d3a051a03eb8d142d7a",
        },
        "19/declaration/body/17/argument/11/expression/right/1/3/1/1/3/1": {
          content: {
            en: "Income to Expense Ratio",
          },
          hash: "76a67e022720179402f6beaadf91c618",
        },
        "19/declaration/body/17/argument/11/expression/right/1/3/1/1/3/3": {
          content: {
            en: "<function:additionalMetrics.incomeToExpenseRatio.toFixed/> : 1",
          },
          hash: "608e16df07d9c3cef691f9d9e8e3a882",
        },
        "19/declaration/body/17/argument/11/expression/right/5/1/1": {
          content: {
            en: "Goals & Budgets",
          },
          hash: "de7106b322640ce5f3dfcd4b302f275a",
        },
        "19/declaration/body/17/argument/11/expression/right/5/1/3": {
          content: {
            en: "Track your active financial plans",
          },
          hash: "f94a55070c2287782d22f9246926c9e6",
        },
        "19/declaration/body/17/argument/11/expression/right/5/3/1/1/3/1": {
          content: {
            en: "Active Financial Goals",
          },
          hash: "bbb3f8fc23c1c37a559db10aa43870c9",
        },
        "19/declaration/body/17/argument/11/expression/right/5/3/1/1/3/3": {
          content: {
            en: "Your current aspirations",
          },
          hash: "78be8af34e478028dad4cfe57e88dd59",
        },
        "19/declaration/body/17/argument/11/expression/right/5/3/3/1/3/1": {
          content: {
            en: "Active Budgets",
          },
          hash: "bcf4e0d60b63efb4f87b74cc39a783dc",
        },
        "19/declaration/body/17/argument/11/expression/right/5/3/3/1/3/3": {
          content: {
            en: "Your current spending plans",
          },
          hash: "1f1ab9833f47b4bdd34eab7b363fd03a",
        },
        "19/declaration/body/17/argument/15/1/3/1/1": {
          content: {
            en: "Financial Goals",
          },
          hash: "7956b1b05f6cc75d5894998d5bdb6c7e",
        },
        "19/declaration/body/17/argument/15/1/3/1/3": {
          content: {
            en: "Your top financial aspirations",
          },
          hash: "1291dfac88093598ace4edeff4555a6b",
        },
        "19/declaration/body/17/argument/15/1/3/3/1/expression/alternate/1": {
          content: {
            en: "No active goals to display.",
          },
          hash: "172e04c13f53c606617bafe7db7a83dd",
        },
        "19/declaration/body/17/argument/15/1/3/3/1/expression/consequent/0/body/5/3":
          {
            content: {
              en: "Target: <function:formatCurrency/>",
            },
            hash: "ab89665f4d4d0bb55326241cd284c770",
          },
        "19/declaration/body/17/argument/15/1/3/3/1/expression/consequent/0/body/7/expression/right":
          {
            content: {
              en: "Save ~<function:formatCurrency/> /month to reach by <expression/>",
            },
            hash: "be0a1f3021461b21dc84fd02de9311c5",
          },
        "19/declaration/body/17/argument/15/1/3/3/3/expression/right": {
          content: {
            en: "View All Goals <element:ArrowRight></element:ArrowRight>",
          },
          hash: "24609f3a5bb1548369c4853e65e40d85",
        },
        "19/declaration/body/17/argument/15/1/7/1/1": {
          content: {
            en: "Accounts",
          },
          hash: "dbb9796c495f5e0f0de6f71bd32527ae",
        },
        "19/declaration/body/17/argument/15/1/7/1/3": {
          content: {
            en: "Manage your money",
          },
          hash: "b39a4cbe43b076697f04d19b4e4fbde3",
        },
        "19/declaration/body/17/argument/15/1/7/3/1/expression/alternate": {
          content: {
            en: "No accounts added yet",
          },
          hash: "18bd74c9f178af9f2079a40ce2e48cb7",
        },
        "19/declaration/body/17/argument/15/1/7/3/1/expression/consequent/1/expression/0/body/1/3/3":
          {
            content: {
              en: "{account.type} • {account.created_by_email}",
            },
            hash: "719eeed761fdc36599b3d093d2ca850a",
          },
        "19/declaration/body/17/argument/15/1/7/3/1/expression/consequent/3": {
          content: {
            en: "View All Accounts <element:ArrowRight></element:ArrowRight>",
          },
          hash: "1546fd7b98daf86429a3bbfac6158370",
        },
        "19/declaration/body/17/argument/15/5/1/1": {
          content: {
            en: "Recent Activity",
          },
          hash: "d4b07b3b4faedbd20762ab8a665d979d",
        },
        "19/declaration/body/17/argument/15/5/1/3": {
          content: {
            en: "Your latest financial activities",
          },
          hash: "4a60f6c1c837d463be5a9bf81b591e6b",
        },
        "19/declaration/body/17/argument/15/5/1/5/1": {
          content: {
            en: "View All <element:ArrowUpRight></element:ArrowUpRight>",
          },
          hash: "b7e74a3f6b75bc7d186edcb627777fb2",
        },
        "19/declaration/body/17/argument/15/5/3/1/expression/alternate/1": {
          content: {
            en: "No recent transactions.",
          },
          hash: "d4ece476f1ee9e7ffc42b044e4b48b4c",
        },
        "19/declaration/body/17/argument/15/5/3/1/expression/consequent/1/1/expression/0/body/1/3":
          {
            content: {
              en: "<function:Date.toLocaleDateString/>  • {transaction.created_by_email}",
            },
            hash: "8da641ec41ba5d448b1abd296b095e08",
          },
        "19/declaration/body/17/argument/3/1/1": {
          content: {
            en: "<element:Home></element:Home> Family Finance",
          },
          hash: "017d5fd1dcc43f10401931b039d9d2d9",
        },
        "19/declaration/body/17/argument/3/1/3": {
          content: {
            en: "/",
          },
          hash: "d727054cb65d0e95c79cfb34bc3e3f28",
        },
        "19/declaration/body/17/argument/3/3/1/3": {
          content: {
            en: "Collaborative financial management for your family group",
          },
          hash: "7316f788362e5f685390d8d2ac9e7cae",
        },
        "19/declaration/body/17/argument/3/3/3/1/1/1": {
          content: {
            en: "<element:Users></element:Users> Members ({groupDetails.members.length})",
          },
          hash: "634b4db31f61795b449c8a7068c0b183",
        },
        "19/declaration/body/17/argument/3/3/3/3/1": {
          content: {
            en: "<element:Settings></element:Settings> Settings",
          },
          hash: "3cf12b88c96fe4bb29fe2c97d443bddb",
        },
        "19/declaration/body/17/argument/3/7/1/1/1/1/1": {
          content: {
            en: "<element:Users></element:Users> Active Members ({groupDetails.members.length})",
          },
          hash: "efdb93f150f84ae05532a34bc58630fa",
        },
        "19/declaration/body/17/argument/3/7/1/1/1/1/3/1": {
          content: {
            en: "<element:UserPlus></element:UserPlus> Invite Members",
          },
          hash: "0da41c538ea1c16a5d00f1bf6e5c8eba",
        },
        "19/declaration/body/17/argument/3/7/1/1/3/1/1/expression/0/body/1/3": {
          content: {
            en: "Joined <function:format/>",
          },
          hash: "f27ed4d587824c1b30e7dcc0005ffd64",
        },
        "19/declaration/body/17/argument/7/expression/right/1/3/1": {
          content: {
            en: "Total Balance",
          },
          hash: "6038d4492072646b17a2e00f53f4ed90",
        },
        "19/declaration/body/17/argument/7/expression/right/1/5/3": {
          content: {
            en: "Across {summary.accounts_count} accounts",
          },
          hash: "92ecc22334a02aed9f403033269cc6ab",
        },
        "19/declaration/body/17/argument/7/expression/right/3/3/1": {
          content: {
            en: "Monthly Income",
          },
          hash: "2bd6a48205b97486e471c3be5a08a01c",
        },
        "19/declaration/body/17/argument/7/expression/right/3/5/3": {
          content: {
            en: "This month",
          },
          hash: "50845a38865204a97773c44dcd2ebb90",
        },
        "19/declaration/body/17/argument/7/expression/right/5/3/1": {
          content: {
            en: "Monthly Expenses",
          },
          hash: "aac0029fa5572d8ff2a9b86ada73a23a",
        },
        "19/declaration/body/17/argument/7/expression/right/5/5/3": {
          content: {
            en: "This month",
          },
          hash: "50845a38865204a97773c44dcd2ebb90",
        },
        "19/declaration/body/17/argument/7/expression/right/7/3/1": {
          content: {
            en: "Savings Rate",
          },
          hash: "718bc855f922b56949efa70e83cd4630",
        },
        "19/declaration/body/17/argument/7/expression/right/7/5/3": {
          content: {
            en: "of income saved this month",
          },
          hash: "d9cab57b603d51d8d32fab4dc040e558",
        },
      },
    },
    "app/dashboard/family/[groupId]/reports/page.tsx": {
      entries: {
        "16/0/init/body/3/expression/0/body/1/3": {
          content: {
            en: "$<function:item.value.toFixed/>",
          },
          hash: "ce3025799d70859c869af0b2fea2ad8a",
        },
        "17/declaration/body/13/consequent/0/argument/1/3": {
          content: {
            en: "Group Reports",
          },
          hash: "5342ca4dc9ea04b8b1c6e85cce3e769a",
        },
        "17/declaration/body/13/consequent/0/argument/3/1/1/3": {
          content: {
            en: "Unable to load group data. Please try again.",
          },
          hash: "5aefe03dfcde02cbd40d3348bcb0f1ee",
        },
        "17/declaration/body/14/argument/1/1/3/1": {
          content: {
            en: "Group Reports",
          },
          hash: "5342ca4dc9ea04b8b1c6e85cce3e769a",
        },
        "17/declaration/body/14/argument/1/1/3/3": {
          content: {
            en: "Analytics for {groupDetails.group_name}",
          },
          hash: "9540e70935e97cec046ecf283afe2a3e",
        },
        "17/declaration/body/14/argument/1/3/1/3/1": {
          content: {
            en: "Last Month",
          },
          hash: "9ab119dff0cb00d094cef2066572fe92",
        },
        "17/declaration/body/14/argument/1/3/1/3/3": {
          content: {
            en: "Last 3 Months",
          },
          hash: "9fe9b397bd8f84a4337c93fef22a7325",
        },
        "17/declaration/body/14/argument/1/3/1/3/5": {
          content: {
            en: "Last 6 Months",
          },
          hash: "40074a0c15af6811bb0688292a2b5e1f",
        },
        "17/declaration/body/14/argument/1/3/1/3/7": {
          content: {
            en: "Last Year",
          },
          hash: "42547108d452358d115c59f8e663e0a5",
        },
        "17/declaration/body/14/argument/1/3/3": {
          content: {
            en: "<element:Download></element:Download> Export",
          },
          hash: "82c7fb0a52965a8cde48deeee9cd663b",
        },
        "17/declaration/body/14/argument/5/expression/right/1/1/1": {
          content: {
            en: "Total Income",
          },
          hash: "7684f6b94ea9df67b234e3679dd702d2",
        },
        "17/declaration/body/14/argument/5/expression/right/1/3/1": {
          content: {
            en: "$<function:summaryStats.totalIncome.toFixed/>",
          },
          hash: "e0c2842ed48b89dde5d9846901fe603e",
        },
        "17/declaration/body/14/argument/5/expression/right/1/3/3": {
          content: {
            en: "<function:format/> - <function:format/>",
          },
          hash: "44bcae57c2b1d155f0ba921274690eb8",
        },
        "17/declaration/body/14/argument/5/expression/right/3/1/1": {
          content: {
            en: "Total Expenses",
          },
          hash: "50e38824a356c1724f41f91e75046542",
        },
        "17/declaration/body/14/argument/5/expression/right/3/3/1": {
          content: {
            en: "$<function:summaryStats.totalExpenses.toFixed/>",
          },
          hash: "e0c2842ed48b89dde5d9846901fe603e",
        },
        "17/declaration/body/14/argument/5/expression/right/3/3/3": {
          content: {
            en: "{summaryStats.transactionCount} transactions",
          },
          hash: "256b3274b64968a0da27c0f8098ece71",
        },
        "17/declaration/body/14/argument/5/expression/right/5/1/1": {
          content: {
            en: "Net Income",
          },
          hash: "6a1b190c2d9f5e15bfc3a32c0be286a0",
        },
        "17/declaration/body/14/argument/5/expression/right/5/3/1": {
          content: {
            en: "$<function:summaryStats.netIncome.toFixed/>",
          },
          hash: "e0c2842ed48b89dde5d9846901fe603e",
        },
        "17/declaration/body/14/argument/5/expression/right/5/3/3": {
          content: {
            en: "Income - Expenses",
          },
          hash: "3c5a9d1d478b2f008efcb5dd5132ffd9",
        },
        "17/declaration/body/14/argument/5/expression/right/7/1/1": {
          content: {
            en: "Avg Transaction",
          },
          hash: "9129222518502e43432e001fda60236f",
        },
        "17/declaration/body/14/argument/5/expression/right/7/3/1": {
          content: {
            en: "$<function:summaryStats.averageTransaction.toFixed/>",
          },
          hash: "e0c2842ed48b89dde5d9846901fe603e",
        },
        "17/declaration/body/14/argument/5/expression/right/7/3/3": {
          content: {
            en: "Per transaction",
          },
          hash: "24112a5db76bf3c0dbd00f525356a7be",
        },
        "17/declaration/body/14/argument/9/1/1": {
          content: {
            en: "<element:BarChart3></element:BarChart3> Overview",
          },
          hash: "6d7d625b84c5de6649c07c4a53c0bb94",
        },
        "17/declaration/body/14/argument/9/1/3": {
          content: {
            en: "<element:Users></element:Users> By Member",
          },
          hash: "92d2892d58ebe8ab0e0ec7ac031eb6d2",
        },
        "17/declaration/body/14/argument/9/1/5": {
          content: {
            en: "<element:PieChart></element:PieChart> Categories",
          },
          hash: "497dc803fcec73cfa002618d7a8bd42b",
        },
        "17/declaration/body/14/argument/9/1/7": {
          content: {
            en: "<element:LineChart></element:LineChart> Trends",
          },
          hash: "91bac3104ad35553e3f6112f36c146ca",
        },
        "17/declaration/body/14/argument/9/3/1/3/1/1": {
          content: {
            en: "Budget Performance",
          },
          hash: "f13dfeede52b06fd338844927dddd88a",
        },
        "17/declaration/body/14/argument/9/3/1/3/1/3": {
          content: {
            en: "How well the group is sticking to budgets",
          },
          hash: "df7fbc99c5ae2bf6d8a3ce952ec3cdf0",
        },
        "17/declaration/body/14/argument/9/3/1/3/3/1/expression/alternate": {
          content: {
            en: "No budgets set up yet.",
          },
          hash: "e7baa759365c13c91aa2dd3d6311798d",
        },
        "17/declaration/body/14/argument/9/3/1/3/3/1/expression/consequent/1/expression/0/body/3/argument/1/3/1":
          {
            content: {
              en: "$<function:spent.toFixed/> / $<function:budget.amount.toFixed/>",
            },
            hash: "947be7f941793bfcba0b5b528aec4d30",
          },
        "17/declaration/body/14/argument/9/3/1/3/3/1/expression/consequent/1/expression/0/body/3/argument/1/3/3":
          {
            content: {
              en: "<function:percentage.toFixed/>%",
            },
            hash: "59c9efe2bb6aca3a531c0af30e24be24",
          },
        "17/declaration/body/14/argument/9/3/1/3/3/1/expression/consequent/1/expression/0/body/3/argument/5/expression/right":
          {
            content: {
              en: "Over budget by $<function:toFixed/>",
            },
            hash: "5a800a96835cc7076f3d612ddd554ec0",
          },
        "17/declaration/body/14/argument/9/3/1/7/1/1": {
          content: {
            en: "Recent Activity",
          },
          hash: "d4b07b3b4faedbd20762ab8a665d979d",
        },
        "17/declaration/body/14/argument/9/3/1/7/1/3": {
          content: {
            en: "Latest transactions in the group",
          },
          hash: "23dae54483a096394d484c04e937413f",
        },
        "17/declaration/body/14/argument/9/3/1/7/3/1/expression/alternate": {
          content: {
            en: "No transactions found.",
          },
          hash: "01023131213960176684d7ad134e0c2d",
        },
        "17/declaration/body/14/argument/9/3/1/7/3/1/expression/consequent/1/expression/0/body/3/1":
          {
            content: {
              en: "<expression/>$<function:Math.abs/>",
            },
            hash: "030f35fe149f17cf1ead23767185108a",
          },
        "17/declaration/body/14/argument/9/5/1/1/1": {
          content: {
            en: "Spending by Member",
          },
          hash: "1509345108cb6ceadbb445945748fd70",
        },
        "17/declaration/body/14/argument/9/5/1/1/3": {
          content: {
            en: "Compare spending across group members",
          },
          hash: "d966bec3c2df38f0ee1e58e5b09e6cf5",
        },
        "17/declaration/body/14/argument/9/5/1/3/1/expression/alternate": {
          content: {
            en: "No spending data available for the selected period.",
          },
          hash: "f5dd2367e8a973d5a3e05035adfcf740",
        },
        "17/declaration/body/14/argument/9/5/1/3/1/expression/consequent-title":
          {
            content: {
              en: "Total Spending by Member",
            },
            hash: "2611474dbf35f15003be224e07f19028",
          },
        "17/declaration/body/14/argument/9/7/1/1/1": {
          content: {
            en: "Spending by Category",
          },
          hash: "5cf4b773ffaee4984451d2138294f9d3",
        },
        "17/declaration/body/14/argument/9/7/1/1/3": {
          content: {
            en: "Top spending categories for the group",
          },
          hash: "e96a6de52942639d5687baf3300cdea4",
        },
        "17/declaration/body/14/argument/9/7/1/3/1/expression/alternate": {
          content: {
            en: "No category data available for the selected period.",
          },
          hash: "46c5e1e2826258604f4c3284811b66d9",
        },
        "17/declaration/body/14/argument/9/7/1/3/1/expression/consequent-title":
          {
            content: {
              en: "Top Spending Categories",
            },
            hash: "d0691ef3ac2b20dea830b7d435168e5c",
          },
        "17/declaration/body/14/argument/9/9/1/1/1": {
          content: {
            en: "Monthly Spending Trends",
          },
          hash: "c04a1cd457b5cc9bce135ed1e13c9189",
        },
        "17/declaration/body/14/argument/9/9/1/1/3": {
          content: {
            en: "Track spending patterns over time",
          },
          hash: "535dd750950c77de77969482b00ea74f",
        },
        "17/declaration/body/14/argument/9/9/1/3/1/expression/alternate": {
          content: {
            en: "No trend data available for the selected period.",
          },
          hash: "9df6c8ad8fb63742a6dd738e99db0205",
        },
        "17/declaration/body/14/argument/9/9/1/3/1/expression/consequent-title":
          {
            content: {
              en: "Monthly Spending",
            },
            hash: "09d43dd6cb9532e10d3c010f73c58a1b",
          },
        "17/declaration/body/14/argument/9/9/5/1/1": {
          content: {
            en: "Group Statistics",
          },
          hash: "de8b82dfdfe42c83bc4b70416921efd5",
        },
        "17/declaration/body/14/argument/9/9/5/1/3": {
          content: {
            en: "Key metrics for {groupDetails.group_name}",
          },
          hash: "86701d6bfaff346c1515093143797459",
        },
        "17/declaration/body/14/argument/9/9/5/3/1/1/3": {
          content: {
            en: "Members",
          },
          hash: "0932e80cba1e3e0a7f52bb67ff31da32",
        },
        "17/declaration/body/14/argument/9/9/5/3/1/3/3": {
          content: {
            en: "Total Transactions",
          },
          hash: "08671e8544cfcc75c6b217aeb3df34c7",
        },
        "17/declaration/body/14/argument/9/9/5/3/1/5/3": {
          content: {
            en: "Active Budgets",
          },
          hash: "1f9241e05eac5df5c3e37c2a773ba775",
        },
        "17/declaration/body/14/argument/9/9/5/3/1/7/1": {
          content: {
            en: "$<expression/>",
          },
          hash: "c4db5dd02d50aaa276f5478caea4fc84",
        },
        "17/declaration/body/14/argument/9/9/5/3/1/7/3": {
          content: {
            en: "Total Balance",
          },
          hash: "fd1144e816cfaf16c396da0540d57f8c",
        },
      },
    },
    "app/dashboard/family/[groupId]/settings/page.tsx": {
      entries: {
        "20/declaration/body/28/consequent/0/argument/1/3": {
          content: {
            en: "Failed to load group details. Please try again.",
          },
          hash: "8d1ee6446b099b1676d6aab0aae4af69",
        },
        "20/declaration/body/29/argument/1/3/1": {
          content: {
            en: "Group Settings",
          },
          hash: "011537462d83582bff421822f3333d88",
        },
        "20/declaration/body/29/argument/1/3/3": {
          content: {
            en: "Manage {groupDetails.group_name} settings and members",
          },
          hash: "fa36ead4b3bb99a957926b0f1a632a2b",
        },
        "20/declaration/body/29/argument/3/1/1": {
          content: {
            en: "<element:Settings></element:Settings> General",
          },
          hash: "0981a14056c95bfbce274bb422b7b528",
        },
        "20/declaration/body/29/argument/3/1/3": {
          content: {
            en: "<element:Users></element:Users> Members",
          },
          hash: "196e65fdb78a0f70f94f57d0f057f7ce",
        },
        "20/declaration/body/29/argument/3/1/5": {
          content: {
            en: "<element:Shield></element:Shield> Advanced",
          },
          hash: "c372ba8c3bf140cb457f57c0e4e90e56",
        },
        "20/declaration/body/29/argument/3/3/1/1/1": {
          content: {
            en: "Group Information",
          },
          hash: "98f237fada2d284d498220d4a7d11ae9",
        },
        "20/declaration/body/29/argument/3/3/1/1/3": {
          content: {
            en: "Basic information about your family group",
          },
          hash: "fc6596648778502f56d5778cf43e1891",
        },
        "20/declaration/body/29/argument/3/3/1/3/1/1": {
          content: {
            en: "Group Name",
          },
          hash: "76797f0870c6436b91ebf3675a3970b8",
        },
        "20/declaration/body/29/argument/3/3/1/3/1/3/1-placeholder": {
          content: {
            en: "Enter group name",
          },
          hash: "fe4fff87cbc04c54a744a60ef48bc9e5",
        },
        "20/declaration/body/29/argument/3/3/1/3/1/3/3/expression/right/1/expression/alternate/3":
          {
            content: {
              en: "Cancel",
            },
            hash: "b46dce13415b15bd9a014ba5847056c0",
          },
        "20/declaration/body/29/argument/3/3/1/3/1/3/3/expression/right/1/expression/consequent":
          {
            content: {
              en: "Edit",
            },
            hash: "f87bea03ff7bb5e837bfa66b7553664a",
          },
        "20/declaration/body/29/argument/3/3/1/3/1/5/expression/right": {
          content: {
            en: "Only group administrators can edit the group name",
          },
          hash: "ce9c7a0763dab577e3c1002145416cc3",
        },
        "20/declaration/body/29/argument/3/3/1/3/3/1": {
          content: {
            en: "Group ID",
          },
          hash: "4ac57295e742d0996b0625436ee6103c",
        },
        "20/declaration/body/29/argument/3/3/1/3/3/5": {
          content: {
            en: "This is your group's unique identifier",
          },
          hash: "d13e00b37cb74584eecb71bd5f23a32b",
        },
        "20/declaration/body/29/argument/3/3/1/3/5/1": {
          content: {
            en: "Created",
          },
          hash: "6045b4ff97fb7dca19ebadfa2e34a700",
        },
        "20/declaration/body/29/argument/3/5/1/1/1": {
          content: {
            en: "Group Members ({groupDetails.members.length})",
          },
          hash: "0c5c8fe9d322fc1fca3eb8725755d281",
        },
        "20/declaration/body/29/argument/3/5/1/1/3": {
          content: {
            en: "Manage member roles and permissions",
          },
          hash: "70140af340356ff33d9ace1ef79d5c9f",
        },
        "20/declaration/body/29/argument/3/5/1/3/1/1/expression/0/body/1/3/3/3/expression/right":
          {
            content: {
              en: "You",
            },
            hash: "db2a4a796b70cc1430d1b21f6ffb6dcb",
          },
        "20/declaration/body/29/argument/3/5/1/3/1/1/expression/0/body/3/expression/right/1/3/1":
          {
            content: {
              en: "Admin",
            },
            hash: "90eb20f1400db82ab874744e47836dc6",
          },
        "20/declaration/body/29/argument/3/5/1/3/1/1/expression/0/body/3/expression/right/1/3/3":
          {
            content: {
              en: "Member",
            },
            hash: "1606dc30b369856b9dba1fe9aec425d2",
          },
        "20/declaration/body/29/argument/3/5/1/3/1/1/expression/0/body/3/expression/right/3/3/1/1":
          {
            content: {
              en: "Remove Member",
            },
            hash: "fa567addd41a5e6ce0feb2b07955cd0a",
          },
        "20/declaration/body/29/argument/3/5/1/3/1/1/expression/0/body/3/expression/right/3/3/1/3":
          {
            content: {
              en: "Are you sure you want to remove {member.email}  from this group? This action cannot be undone.",
            },
            hash: "a14fae7f374aa5eed3be9030eb2d9f34",
          },
        "20/declaration/body/29/argument/3/5/1/3/1/1/expression/0/body/3/expression/right/3/3/3/1":
          {
            content: {
              en: "Cancel",
            },
            hash: "2e2a849c2223911717de8caa2c71bade",
          },
        "20/declaration/body/29/argument/3/5/1/3/1/1/expression/0/body/3/expression/right/3/3/3/3":
          {
            content: {
              en: "<expression/> Remove Member",
            },
            hash: "ce96b71ea3fcab387f44a43e27afa9c2",
          },
        "20/declaration/body/29/argument/3/5/1/3/5/expression/right/3/1/1": {
          content: {
            en: "Invite New Member",
          },
          hash: "e58931b8a0325875221751a870254573",
        },
        "20/declaration/body/29/argument/3/5/1/3/5/expression/right/3/1/3": {
          content: {
            en: "Send an invitation to join this family group",
          },
          hash: "b3d74bd85a11904fe64933b7fa5e8637",
        },
        "20/declaration/body/29/argument/3/5/1/3/5/expression/right/3/3/1/1": {
          content: {
            en: "Email address",
          },
          hash: "db755463918c579ec099c7639de0a685",
        },
        "20/declaration/body/29/argument/3/5/1/3/5/expression/right/3/3/1/3-placeholder":
          {
            content: {
              en: "Enter email address",
            },
            hash: "91ee3380904819d9c5af2947b410a7c5",
          },
        "20/declaration/body/29/argument/3/5/1/3/5/expression/right/3/3/3": {
          content: {
            en: "<expression/> Send Invite",
          },
          hash: "419cdfc88cd6bea8fa32f1ae5e97df92",
        },
        "20/declaration/body/29/argument/3/5/1/3/5/expression/right/7/expression/right/3/1/1":
          {
            content: {
              en: "Pending Invitations ({pendingInvitations.length})",
            },
            hash: "f1d07c5f5b939d0de5dd55f8458f4a51",
          },
        "20/declaration/body/29/argument/3/5/1/3/5/expression/right/7/expression/right/3/1/3":
          {
            content: {
              en: "Invitations that haven't been accepted yet",
            },
            hash: "b6b57da6e30fea9fba3bf4b9ab2efee0",
          },
        "20/declaration/body/29/argument/3/5/1/3/5/expression/right/7/expression/right/3/3/1/expression/0/body/1/3/3":
          {
            content: {
              en: "Invited on <function:Date.toLocaleDateString/><expression/> Expires <function:Date.toLocaleDateString/>",
            },
            hash: "bff0febfc49278fc31beda288e04440f",
          },
        "20/declaration/body/29/argument/3/5/1/3/5/expression/right/7/expression/right/3/3/1/expression/0/body/3/1":
          {
            content: {
              en: "Pending",
            },
            hash: "030a6f3395d5d4efddd3cc67d6009039",
          },
        "20/declaration/body/29/argument/3/7/1/1/1": {
          content: {
            en: "Notification Settings",
          },
          hash: "41ea1c4757d87aa8f9bfddc18a3c8163",
        },
        "20/declaration/body/29/argument/3/7/1/1/3": {
          content: {
            en: "Configure how you receive notifications for this group",
          },
          hash: "4a4e17acd432787f70376a8d783bbc10",
        },
        "20/declaration/body/29/argument/3/7/1/3/1/1/1": {
          content: {
            en: "Transaction Notifications",
          },
          hash: "4f83f6c81ced2dac19feba4aa03f0612",
        },
        "20/declaration/body/29/argument/3/7/1/3/1/1/3": {
          content: {
            en: "Get notified when new transactions are added",
          },
          hash: "f539c0a39a97c7b9e35fc0c1e8ebbe31",
        },
        "20/declaration/body/29/argument/3/7/1/3/5/1/1": {
          content: {
            en: "Budget Alerts",
          },
          hash: "b76c00d753ffdf7b25393124a284de76",
        },
        "20/declaration/body/29/argument/3/7/1/3/5/1/3": {
          content: {
            en: "Receive alerts when budgets are exceeded",
          },
          hash: "17387c90fc0a4ad7d69f0188dbd80bad",
        },
        "20/declaration/body/29/argument/3/7/1/3/9/1/1": {
          content: {
            en: "Member Activity",
          },
          hash: "36109b3f6c5c4151b054aa7d0a53fe74",
        },
        "20/declaration/body/29/argument/3/7/1/3/9/1/3": {
          content: {
            en: "Get notified about member joins and leaves",
          },
          hash: "48be87cf5c97f233444dc9b9a031c06c",
        },
        "20/declaration/body/29/argument/3/7/3/expression/right/1/1": {
          content: {
            en: "Danger Zone",
          },
          hash: "ab5417cabdfa70b0a7c9d407ec69b450",
        },
        "20/declaration/body/29/argument/3/7/3/expression/right/1/3": {
          content: {
            en: "Irreversible and destructive actions",
          },
          hash: "beb9292e0732f1a65be4b1fb9bac4fa2",
        },
        "20/declaration/body/29/argument/3/7/3/expression/right/3/1/3": {
          content: {
            en: "Deleting a group will permanently remove all associated data including transactions, budgets, and goals. This action cannot be undone.",
          },
          hash: "6cdad3d0ef761083aa4ea63899d3f6c4",
        },
        "20/declaration/body/29/argument/3/7/3/expression/right/3/3/1/1": {
          content: {
            en: "<element:Trash2></element:Trash2> Delete Group",
          },
          hash: "a7bcdea88f6e8c46b110173a26b829dd",
        },
        "20/declaration/body/29/argument/3/7/3/expression/right/3/3/3/1/1": {
          content: {
            en: "Delete Group",
          },
          hash: "b42008268d3023b1152bc3720d6742d8",
        },
        "20/declaration/body/29/argument/3/7/3/expression/right/3/3/3/1/3": {
          content: {
            en: 'This action cannot be undone. This will permanently delete the group "{groupDetails.group_name}" and all associated data.',
          },
          hash: "c1bef3bc1f69b6b902fed78d8becdd44",
        },
        "20/declaration/body/29/argument/3/7/3/expression/right/3/3/3/3/1/1": {
          content: {
            en: "Type <element:code>DELETE</element:code>  to confirm:",
          },
          hash: "a71249cc2fc0eee3d7b54281a4a68783",
        },
        "20/declaration/body/29/argument/3/7/3/expression/right/3/3/3/3/1/3-placeholder":
          {
            content: {
              en: "Type DELETE to confirm",
            },
            hash: "e3b109c0649f38e2126d482cf60bf745",
          },
        "20/declaration/body/29/argument/3/7/3/expression/right/3/3/3/5/1": {
          content: {
            en: "Cancel",
          },
          hash: "1230d21c067e341f36a3d7f71c0a0bd8",
        },
      },
    },
    "app/dashboard/family/[groupId]/subscriptions/page.tsx": {
      entries: {
        "15/declaration/body/21/argument/11/1/3-placeholder": {
          content: {
            en: "Search family subscriptions...",
          },
          hash: "3e0ac7b8a687e0c03d6676d0526077a9",
        },
        "15/declaration/body/21/argument/11/5/3/1/expression/0/body": {
          content: {
            en: "Sort by {option.label}",
          },
          hash: "9ab97da0073e1e40c6ea6f51a932dcd3",
        },
        "15/declaration/body/21/argument/15/expression/alternate/consequent/1/7/expression/right":
          {
            content: {
              en: "<element:Plus></element:Plus> Add Your First Family Subscription",
            },
            hash: "b3519cea27a78ea459b54a31964ee244",
          },
        "15/declaration/body/21/argument/3/1/1": {
          content: {
            en: "<element:Users></element:Users> Family Subscriptions",
          },
          hash: "d3bce18d4da18d675e54e3448109f9b7",
        },
        "15/declaration/body/21/argument/3/1/3": {
          content: {
            en: "Manage your family's shared recurring payments and bills",
          },
          hash: "f3befd580e23afe1edb571e38ff67d02",
        },
        "15/declaration/body/21/argument/3/3": {
          content: {
            en: "<element:Plus></element:Plus> Add Family Subscription",
          },
          hash: "4b7198fa5f8f9e9e0adb8c652a97e9cb",
        },
        "15/declaration/body/21/argument/7/expression/right/1/1/1/3/3": {
          content: {
            en: "Family Monthly Total",
          },
          hash: "38fe3f55ded573a1b24d9c2b48ba91c7",
        },
        "15/declaration/body/21/argument/7/expression/right/3/1/1/3/3": {
          content: {
            en: "Active Shared",
          },
          hash: "ab20379f4e4e7427391a7c3a874c1e14",
        },
        "15/declaration/body/21/argument/7/expression/right/5/1/1/3/3": {
          content: {
            en: "Due This Week",
          },
          hash: "f0cb6d8d94d4496308514d8807df14de",
        },
        "15/declaration/body/21/argument/7/expression/right/7/1/1/3/3": {
          content: {
            en: "Expiring Soon",
          },
          hash: "0e149c65f320079c950db404558f6a4b",
        },
      },
    },
    "app/dashboard/family/[groupId]/transactions/page.tsx": {
      entries: {
        "28/declaration/body/28/consequent/0/argument/1/1/3": {
          content: {
            en: "Loading transactions...",
          },
          hash: "ef095462e19def338a51a5b15d18162e",
        },
        "28/declaration/body/29/argument/3/1/1/1": {
          content: {
            en: "<element:Home></element:Home> Family Finance",
          },
          hash: "79de5499e77a22740841354a18cdecb8",
        },
        "28/declaration/body/29/argument/3/1/1/3": {
          content: {
            en: "/",
          },
          hash: "d727054cb65d0e95c79cfb34bc3e3f28",
        },
        "28/declaration/body/29/argument/3/1/1/5": {
          content: {
            en: "Group Dashboard",
          },
          hash: "cf8794641808c11d9c19873ef61492f2",
        },
        "28/declaration/body/29/argument/3/1/1/7": {
          content: {
            en: "/",
          },
          hash: "d727054cb65d0e95c79cfb34bc3e3f28",
        },
        "28/declaration/body/29/argument/3/1/1/9": {
          content: {
            en: "Transactions",
          },
          hash: "59973447457c6bd57c161d594f551651",
        },
        "28/declaration/body/29/argument/3/1/3": {
          content: {
            en: "Family Transactions",
          },
          hash: "235e6cb34741d8fc028ffa88b4efd2c3",
        },
        "28/declaration/body/29/argument/3/1/5": {
          content: {
            en: "View and manage all transactions for your family group",
          },
          hash: "aa252c305c2bfc901f3e8717be8a820a",
        },
        "28/declaration/body/29/argument/3/3/1/1": {
          content: {
            en: "<element:PlusCircle></element:PlusCircle> Add Transaction",
          },
          hash: "45285da53016aae4595b845139b38902",
        },
        "28/declaration/body/29/argument/3/3/3/3/1/1": {
          content: {
            en: "Description",
          },
          hash: "e17686a22ffad04cc7bb70524ed4478b",
        },
        "28/declaration/body/29/argument/3/3/3/3/1/3/openingElement/2/value/expression/body-placeholder":
          {
            content: {
              en: "Enter transaction description",
            },
            hash: "c7a11b94e00ee35afec9b506a955b303",
          },
        "28/declaration/body/29/argument/3/3/3/3/11/1/1": {
          content: {
            en: "Cancel",
          },
          hash: "643431b4a45271ffb02d2957be7884b3",
        },
        "28/declaration/body/29/argument/3/3/3/3/3/1/1": {
          content: {
            en: "Amount",
          },
          hash: "a0ae9c6ff98b57475cdce56f66066aa4",
        },
        "28/declaration/body/29/argument/3/3/3/3/3/1/3/openingElement/2/value/expression/body-placeholder":
          {
            content: {
              en: "0.00",
            },
            hash: "5d9d7db256e581afdc5ab7e5196b009e",
          },
        "28/declaration/body/29/argument/3/3/3/3/3/3/1": {
          content: {
            en: "Type",
          },
          hash: "f04471a7ddac844b9ad145eb9911ef75",
        },
        "28/declaration/body/29/argument/3/3/3/3/3/3/3/openingElement/2/value/expression/body/1/1-placeholder":
          {
            content: {
              en: "Select type",
            },
            hash: "fa373e47f55ff081982844a853be3a88",
          },
        "28/declaration/body/29/argument/3/3/3/3/3/3/3/openingElement/2/value/expression/body/3/1":
          {
            content: {
              en: "Income",
            },
            hash: "398bff8476817f942c70c4aadad5dd91",
          },
        "28/declaration/body/29/argument/3/3/3/3/3/3/3/openingElement/2/value/expression/body/3/3":
          {
            content: {
              en: "Expense",
            },
            hash: "5d71ac3af27428af3058019d1cbfcc61",
          },
        "28/declaration/body/29/argument/3/3/3/3/5/1": {
          content: {
            en: "Date",
          },
          hash: "56f41c5d30a76295bb087b20b7bee4c3",
        },
        "28/declaration/body/29/argument/3/3/3/3/7/1": {
          content: {
            en: "Account",
          },
          hash: "01215c12fb1cdb93bd0c84c1382bef56",
        },
        "28/declaration/body/29/argument/3/3/3/3/7/3/openingElement/2/value/expression/body/1/1-placeholder":
          {
            content: {
              en: "Select account",
            },
            hash: "1dbc9f1c405b721f8d44e25865e49128",
          },
        "28/declaration/body/29/argument/3/3/3/3/7/3/openingElement/2/value/expression/body/3/1/expression/0/body":
          {
            content: {
              en: "{account.name} ({account.type})",
            },
            hash: "9d7396235ee0cdf315e33347bd7d88bf",
          },
        "28/declaration/body/29/argument/3/3/3/3/9/1": {
          content: {
            en: "Category (Optional)",
          },
          hash: "bff671a5e11b4d8e264ba8deef37d8ee",
        },
        "28/declaration/body/29/argument/3/3/3/3/9/3/openingElement/2/value/expression/body/1/1-placeholder":
          {
            content: {
              en: "Select category",
            },
            hash: "48c701bd440d494da6a6fac2570e7196",
          },
        "28/declaration/body/29/argument/3/3/3/3/9/3/openingElement/2/value/expression/body/3/1":
          {
            content: {
              en: "No Category",
            },
            hash: "0bc04793eabacb8d4352d74d05678947",
          },
        "28/declaration/body/29/argument/7/1/1": {
          content: {
            en: "All Transactions",
          },
          hash: "0788f9fe612abf449a2423d6d6ecde9f",
        },
        "28/declaration/body/29/argument/7/3/1/expression/alternate/3": {
          content: {
            en: "No transactions yet",
          },
          hash: "5e26656469d7591a44c9bfa9ded7e938",
        },
        "28/declaration/body/29/argument/7/3/1/expression/alternate/5": {
          content: {
            en: "Start by adding your first transaction to track your family's finances.",
          },
          hash: "4393b609052fa86fe8b6170680e97c9c",
        },
        "28/declaration/body/29/argument/7/3/1/expression/alternate/7": {
          content: {
            en: "<element:PlusCircle></element:PlusCircle> Add First Transaction",
          },
          hash: "69393195679bb9e75a1619c7ea875cfc",
        },
        "28/declaration/body/29/argument/7/3/1/expression/consequent/1/1/1/1": {
          content: {
            en: "Date",
          },
          hash: "56f41c5d30a76295bb087b20b7bee4c3",
        },
        "28/declaration/body/29/argument/7/3/1/expression/consequent/1/1/1/11":
          {
            content: {
              en: "Amount",
            },
            hash: "a0ae9c6ff98b57475cdce56f66066aa4",
          },
        "28/declaration/body/29/argument/7/3/1/expression/consequent/1/1/1/13":
          {
            content: {
              en: "Actions",
            },
            hash: "c46571856723b03262fd33f511116298",
          },
        "28/declaration/body/29/argument/7/3/1/expression/consequent/1/1/1/3": {
          content: {
            en: "Description",
          },
          hash: "e17686a22ffad04cc7bb70524ed4478b",
        },
        "28/declaration/body/29/argument/7/3/1/expression/consequent/1/1/1/5": {
          content: {
            en: "Category",
          },
          hash: "1b0340cd175aa5a2be745a0d54908466",
        },
        "28/declaration/body/29/argument/7/3/1/expression/consequent/1/1/1/7": {
          content: {
            en: "Account",
          },
          hash: "01215c12fb1cdb93bd0c84c1382bef56",
        },
        "28/declaration/body/29/argument/7/3/1/expression/consequent/1/1/1/9": {
          content: {
            en: "Created By",
          },
          hash: "d6344f18a224d12c18933cc162389cf4",
        },
      },
    },
    "app/dashboard/family/page.tsx": {
      entries: {
        "9/declaration/body/1/consequent/0/argument/3": {
          content: {
            en: "Loading family groups...",
          },
          hash: "804a42d3a69be4aa04504c5c71e228b9",
        },
        "9/declaration/body/2/consequent/0/argument/1/1": {
          content: {
            en: "<element:Users></element:Users> Family Finance",
          },
          hash: "017d5fd1dcc43f10401931b039d9d2d9",
        },
        "9/declaration/body/2/consequent/0/argument/1/3": {
          content: {
            en: "Manage your family's shared finances, create groups, and collaborate on budgets.",
          },
          hash: "580be50c70709ada66bfda0769ec52f0",
        },
        "9/declaration/body/2/consequent/0/argument/3/1/1": {
          content: {
            en: "<element:AlertCircle></element:AlertCircle> Error",
          },
          hash: "a49b98d70a8da736f23bcd348e0580ea",
        },
        "9/declaration/body/2/consequent/0/argument/3/3/1": {
          content: {
            en: "Could not load family groups: {error.message}",
          },
          hash: "671abfe3e6b52c3a8f82a14ee39c2733",
        },
        "9/declaration/body/4/argument/1/1": {
          content: {
            en: "<element:Users></element:Users> Family Finance",
          },
          hash: "5b97ef903a0abb4fd1ae8fb213bb55e2",
        },
        "9/declaration/body/4/argument/1/3": {
          content: {
            en: "Manage your family's shared finances, create groups, and collaborate on budgets.",
          },
          hash: "f2fd3a89896126e7b02728d5cb679889",
        },
        "9/declaration/body/4/argument/5/1/1/1": {
          content: {
            en: "<element:Plus></element:Plus> Create Family Group",
          },
          hash: "162c126e4f6a851090eb4ea706b93a9b",
        },
        "9/declaration/body/4/argument/5/1/3/1": {
          content: {
            en: "Start a new family group to manage shared expenses and budgets together.",
          },
          hash: "41974457f51438a61c3cf229a7136b2c",
        },
        "9/declaration/body/4/argument/5/1/3/3": {
          content: {
            en: "Get Started<element:ArrowRight></element:ArrowRight>",
          },
          hash: "f3d31b25d643f122e94bab1936cc9bc4",
        },
        "9/declaration/body/4/argument/5/3/1/1": {
          content: {
            en: "<element:Heart></element:Heart> Join Family Group",
          },
          hash: "a2d7a09c507585ab3166b9b79dc1a22d",
        },
        "9/declaration/body/4/argument/5/3/3/1": {
          content: {
            en: "Join an existing family group using an invitation link or code.",
          },
          hash: "f1b76a8416f2703be251b25b4a7f0d2c",
        },
        "9/declaration/body/4/argument/5/3/3/3/1": {
          content: {
            en: "View Invitations<element:ArrowRight></element:ArrowRight>",
          },
          hash: "234156c07c58968940d0e202473025c6",
        },
        "9/declaration/body/4/argument/5/5/1/1": {
          content: {
            en: "<element:Shield></element:Shield> Security & Privacy",
          },
          hash: "11339338089752ac4eead5228ea588ef",
        },
        "9/declaration/body/4/argument/5/5/3/1": {
          content: {
            en: "Learn about family group security and privacy settings.",
          },
          hash: "88ad3820550c8a7de34f590510ad07be",
        },
        "9/declaration/body/4/argument/5/5/3/3": {
          content: {
            en: "Learn More<element:ArrowRight></element:ArrowRight>",
          },
          hash: "ee4ca333a1acdc5e5ce41793f6e5155f",
        },
        "9/declaration/body/4/argument/9/3/1/1/1": {
          content: {
            en: "<element:Users></element:Users> Your Family Groups",
          },
          hash: "151838d0dfdc274bb36553edd4b85ad0",
        },
        "9/declaration/body/4/argument/9/3/3/1/expression/alternate/1/expression/0/body/1/1/3/1/3/1/expression/alternate":
          {
            content: {
              en: "<element:Users></element:Users> Member",
            },
            hash: "a85f7f60a5cd66f524cb80d8b011ac1f",
          },
        "9/declaration/body/4/argument/9/3/3/1/expression/alternate/1/expression/0/body/1/1/3/1/3/1/expression/consequent":
          {
            content: {
              en: "<element:Shield></element:Shield> Admin",
            },
            hash: "e1e08eb77300ac52f602cf992b8280cc",
          },
        "9/declaration/body/4/argument/9/3/3/1/expression/alternate/1/expression/0/body/1/1/3/3":
          {
            content: {
              en: "Created <function:Date.toLocaleDateString/>",
            },
            hash: "3e118f1706ca4022beeaa455bdbf958c",
          },
        "9/declaration/body/4/argument/9/3/3/1/expression/alternate/1/expression/0/body/1/3/1/1":
          {
            content: {
              en: "<element:DollarSign></element:DollarSign> Manage<element:ArrowRight></element:ArrowRight>",
            },
            hash: "222694490053201615efe844f0910938",
          },
        "9/declaration/body/4/argument/9/3/3/1/expression/consequent/3": {
          content: {
            en: "No Family Groups Yet",
          },
          hash: "6071eb6b7f99a5a3c4be2cd1d20213d9",
        },
        "9/declaration/body/4/argument/9/3/3/1/expression/consequent/5": {
          content: {
            en: "Create or join a family group to start collaborating on finances.",
          },
          hash: "00df79effb1f5beb65ac9d8d8e1858b4",
        },
        "9/declaration/body/4/argument/9/3/3/1/expression/consequent/7": {
          content: {
            en: "<element:Plus></element:Plus> Create Your First Group",
          },
          hash: "381f77aba8fc1244020eb356a4c768e8",
        },
        "9/declaration/body/4/argument/9/7/1/1": {
          content: {
            en: "<element:Target></element:Target> Family Features",
          },
          hash: "15bbd6964738ebe8b454722ccfb37bf5",
        },
        "9/declaration/body/4/argument/9/7/3/1/1/3/1": {
          content: {
            en: "Shared Budgets",
          },
          hash: "6be1129d5533d76066b0b8006312c4d1",
        },
        "9/declaration/body/4/argument/9/7/3/1/1/3/3": {
          content: {
            en: "Create and manage budgets that everyone can contribute to.",
          },
          hash: "14f49e72e420673d6273405844d1b4cb",
        },
        "9/declaration/body/4/argument/9/7/3/1/3/3/1": {
          content: {
            en: "Safe Collaboration",
          },
          hash: "6afa8bb962b742b38057a59240b1b158",
        },
        "9/declaration/body/4/argument/9/7/3/1/3/3/3": {
          content: {
            en: "Secure permissions and privacy controls for family members.",
          },
          hash: "49a2f0cca0fd0f4c4a2b1ab76714a8e3",
        },
        "9/declaration/body/4/argument/9/7/3/1/5/3/1": {
          content: {
            en: "Family Goals",
          },
          hash: "4b533ffdf6a70e83f3bae0f0acca7b61",
        },
        "9/declaration/body/4/argument/9/7/3/1/5/3/3": {
          content: {
            en: "Set and track financial goals together as a family.",
          },
          hash: "dd75426810df634f39f26f30123ff257",
        },
        "9/declaration/body/4/argument/9/7/3/1/7/3/1": {
          content: {
            en: "Joint Planning",
          },
          hash: "8e1087887d678b565a344e3b2a451037",
        },
        "9/declaration/body/4/argument/9/7/3/1/7/3/3": {
          content: {
            en: "Plan financial decisions together with shared insights.",
          },
          hash: "ad97384d5b8fe486d7aeee7f8bb134e0",
        },
      },
    },
    "app/dashboard/goals/page.tsx": {
      entries: {
        "23/declaration/body/14/0/init/body/0/0/0/argument": {
          content: {
            en: "<element:CheckCircle2></element:CheckCircle2> Completed",
          },
          hash: "1cafffb7b5aef00280bd28d977c7ab56",
        },
        "23/declaration/body/14/0/init/body/0/1/0/argument": {
          content: {
            en: "<element:Pause></element:Pause> Paused",
          },
          hash: "f4440257441487ec2248f7638ed7432e",
        },
        "23/declaration/body/14/0/init/body/0/2/0/argument": {
          content: {
            en: "<element:Target></element:Target> Active",
          },
          hash: "179922d069fa59cb2f862e5ba2804d42",
        },
        "23/declaration/body/22/argument/1/3/1/1": {
          content: {
            en: "<element:Plus></element:Plus> New Goal",
          },
          hash: "d334c64bf19add29b449a705d89bc7c2",
        },
        "23/declaration/body/22/argument/1/3/3/1/3": {
          content: {
            en: "Set a financial target to work towards",
          },
          hash: "2df6f721ac5b633135f15e0ece5a8bc4",
        },
        "23/declaration/body/22/argument/1/3/3/3/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Goal Name",
            },
            hash: "c2935a53240b1ded4f1072f5d505dda0",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "e.g., Emergency Fund",
            },
            hash: "1cee903ac7636971bbbde7e7e4bead3e",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/11/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Color",
            },
            hash: "9d53d1d120e8b8954bcae9a322573748",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/13/1": {
          content: {
            en: "<expression/> Goal",
          },
          hash: "1f55a52c2b852591aeaae7081885b723",
        },
        "23/declaration/body/22/argument/1/3/3/3/1/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Target Amount",
            },
            hash: "4c5ef34bfffe9afe09c952876641741a",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/3/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "1000",
            },
            hash: "a9b71fb63c03fa90677aadd388619f52",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Current Amount",
            },
            hash: "1b3b5c106a2ce5a955a3f995d6a1c368",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "0",
            },
            hash: "680f7639d6def9a224135b5a2512ebf3",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/5/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "How much you've already saved",
            },
            hash: "d17b97946d2ed14a8beb967c6a04f9ed",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Currency",
            },
            hash: "d2ba9a9cea418ea653e3ca6785617a3f",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select currency",
            },
            hash: "a7604892fc4bfe50ead2ba5d5dd95c9c",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/1/3/1":
          {
            content: {
              en: "USD ($)",
            },
            hash: "70d998854c94afcefc169b57e0b880cc",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/1/3/3":
          {
            content: {
              en: "EUR (€)",
            },
            hash: "b6e2713de3d3bd0f30508582bb5e82df",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/1/3/5":
          {
            content: {
              en: "GBP (£)",
            },
            hash: "f972658cf8cff55ad6c55801e1bb7bef",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/1/3/7":
          {
            content: {
              en: "JPY (¥)",
            },
            hash: "92e310366597441706d9a62811db554e",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/7/openingElement/2/value/expression/body/3/1/3/9":
          {
            content: {
              en: "CAD ($)",
            },
            hash: "e76b742cccb277d4580ab4bc3a4a135d",
          },
        "23/declaration/body/22/argument/1/3/3/3/1/9/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Deadline",
            },
            hash: "fc40737658068c88c645eed6ee9342d4",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/1/1/1/3/5/expression/right":
          {
            content: {
              en: "{goal.daysRemaining} days left",
            },
            hash: "1a47f12fb631e6e9defa0fc380ce29de",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/3/1/1/1":
          {
            content: {
              en: "Progress",
            },
            hash: "dd0200d5849ebb7d64c15098ae91d229",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/3/1/1/3":
          {
            content: {
              en: "<function:formatCurrency/> / <function:formatCurrency/>",
            },
            hash: "06a3a8526dac38e0b383a7db3fca1448",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/3/1/5/1":
          {
            content: {
              en: "<function:goal.progress.toFixed/>% complete",
            },
            hash: "93ba299eaff2ba47a295de7db797764c",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/3/1/5/3/expression/right":
          {
            content: {
              en: "<expression/> /month needed",
            },
            hash: "fe2beaf6445d672442bc06b8d644b809",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/3/3/expression/right/1/1/1":
          {
            content: {
              en: "<element:Plus></element:Plus> Add Progress",
            },
            hash: "db53592f6e52e318679183115a81fb14",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/3/3/expression/right/1/3/1/1":
          {
            content: {
              en: "Add Progress",
            },
            hash: "8d3203792a38f5abce26ca372843ae60",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/3/3/expression/right/1/3/1/3":
          {
            content: {
              en: "How much would you like to add to this goal?",
            },
            hash: "a469fa1ef88cf796b3e3704b2376f127",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/3/3/expression/right/1/3/3/1-placeholder":
          {
            content: {
              en: "0.00",
            },
            hash: "5d9d7db256e581afdc5ab7e5196b009e",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/3/3/expression/right/1/3/3/3/1":
          {
            content: {
              en: "Add Amount",
            },
            hash: "c8554baea013fa6cf640883cba8076b7",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/3/5/expression/right":
          {
            content: {
              en: "<element:Play></element:Play> Resume Goal",
            },
            hash: "048be87d2925b0da65ac2e0e285e3ed0",
          },
        "23/declaration/body/22/argument/13/expression/alternate/1/expression/0/body/1/argument/3/7/expression/right/3":
          {
            content: {
              en: "Goal Achieved!",
            },
            hash: "b48594c7b5074c5a337223c89ab47aea",
          },
        "23/declaration/body/22/argument/13/expression/consequent/1/3": {
          content: {
            en: "No goals yet",
          },
          hash: "737010e6d019fd628888e70c69110e01",
        },
        "23/declaration/body/22/argument/13/expression/consequent/1/5": {
          content: {
            en: "Create your first financial goal to start tracking progress",
          },
          hash: "556608c65e919fb1c85925ae387d8a77",
        },
        "23/declaration/body/22/argument/13/expression/consequent/1/7": {
          content: {
            en: "<element:Plus></element:Plus> Create Goal",
          },
          hash: "d92ecee0b0fa7ad0d1a0f941e304052d",
        },
        "23/declaration/body/22/argument/5/expression/right/1": {
          content: {
            en: "<element:AlertTriangle></element:AlertTriangle> Goal Alerts",
          },
          hash: "965e3ce6f05f363a1d4fc1dc063c24f1",
        },
        "23/declaration/body/22/argument/5/expression/right/3/1/expression/0/body/1/argument/1/1/3/1":
          {
            content: {
              en: "<function:goal.progress.toFixed/>% complete",
            },
            hash: "c71fc24ebfd91d61ef25a749b7a18b3e",
          },
        "23/declaration/body/22/argument/5/expression/right/3/1/expression/0/body/1/argument/1/1/3/3":
          {
            content: {
              en: "<element:Edit></element:Edit> Edit",
            },
            hash: "82449403ba23e100a26b20cf48605364",
          },
        "23/declaration/body/22/argument/9/1/3/1": {
          content: {
            en: "Active Goals",
          },
          hash: "5a4cd2daefe4889a113ebcec336fa50a",
        },
        "23/declaration/body/22/argument/9/1/5/3": {
          content: {
            en: "Currently working on",
          },
          hash: "f4b002231ca541205590546511396018",
        },
        "23/declaration/body/22/argument/9/3/3/1": {
          content: {
            en: "Completed Goals",
          },
          hash: "71686ff961aa269e1efea48f228b642b",
        },
        "23/declaration/body/22/argument/9/3/5/3": {
          content: {
            en: "Successfully achieved",
          },
          hash: "0c0605238c062abaf2e7fe5db401cf0f",
        },
        "23/declaration/body/22/argument/9/5/3/1": {
          content: {
            en: "Total Saved",
          },
          hash: "6094cdb3a2d3c5a7dff6f1372a504f2c",
        },
        "23/declaration/body/22/argument/9/5/5/3": {
          content: {
            en: "Across all goals",
          },
          hash: "8d2dcd2c450544fe0d7afe61137ecc18",
        },
        "23/declaration/body/22/argument/9/7/3/1": {
          content: {
            en: "Overall Progress",
          },
          hash: "f15c1c565b2d8b4ef5392d0b045ede5b",
        },
        "23/declaration/body/22/argument/9/7/5/1": {
          content: {
            en: "<expression/> %",
          },
          hash: "9ca6a5d2340b8092d1b6f486ed4b8f8e",
        },
        "23/declaration/body/22/argument/9/7/5/3": {
          content: {
            en: "Of total target",
          },
          hash: "8874e719191f556c6235aedcf115cf3c",
        },
      },
    },
    "app/dashboard/invitations/page.tsx": {
      entries: {
        "9/declaration/body/4/consequent/0/argument/3": {
          content: {
            en: "Loading your invitations...",
          },
          hash: "80db40a4345c56caebfe611068f54ecb",
        },
        "9/declaration/body/5/consequent/0/argument/1/1/1": {
          content: {
            en: "<element:MailWarning></element:MailWarning> Error Loading Invitations",
          },
          hash: "e9d20574014b69563b7d842164890847",
        },
        "9/declaration/body/5/consequent/0/argument/1/3/1": {
          content: {
            en: "There was a problem fetching your invitations. Please try again later.",
          },
          hash: "bdcb750340dd2c3750de0f878000fcbc",
        },
        "9/declaration/body/5/consequent/0/argument/1/3/3/expression/right": {
          content: {
            en: "Details: {error.message}",
          },
          hash: "4482d2bf4fa8c450a80185289030afc2",
        },
        "9/declaration/body/6/argument/1/1": {
          content: {
            en: "Family Finance Invitations",
          },
          hash: "950edd4dd40f6198678630bc668b480f",
        },
        "9/declaration/body/6/argument/1/3": {
          content: {
            en: "View and respond to pending family finance group invitations.",
          },
          hash: "8d4179e9dbef4ed4a6880ac394102e5c",
        },
        "9/declaration/body/6/argument/3/expression/alternate/3": {
          content: {
            en: "No Pending Invitations",
          },
          hash: "749f54f16bd2e35fefe3910c4c67f82c",
        },
        "9/declaration/body/6/argument/3/expression/alternate/5": {
          content: {
            en: "You currently have no pending invitations to join any family finance groups.",
          },
          hash: "4007919437f15f430cb1cedc1dbfdc71",
        },
        "9/declaration/body/6/argument/3/expression/consequent/1/expression/0/body/1/1":
          {
            content: {
              en: "Invitation to join: {invite.group_name}",
            },
            hash: "d5746638e09a40307051f80ca456e106",
          },
        "9/declaration/body/6/argument/3/expression/consequent/1/expression/0/body/1/3":
          {
            content: {
              en: "Invited by: {invite.invited_by_user_email} on <function:format/>",
            },
            hash: "7997dc1a30d8d1327f09945c8b6d91fc",
          },
        "9/declaration/body/6/argument/3/expression/consequent/1/expression/0/body/3/1":
          {
            content: {
              en: 'You have been invited to join the family finance group "<element:strong>{invite.group_name}</element:strong>" to manage shared finances together.',
            },
            hash: "b29f1d6bfc83c6ff81e1e5da44a7e58b",
          },
        "9/declaration/body/6/argument/3/expression/consequent/1/expression/0/body/5/1":
          {
            content: {
              en: "<expression/> Decline",
            },
            hash: "6a6f3925568c0ac9a688c6a5200579a6",
          },
        "9/declaration/body/6/argument/3/expression/consequent/1/expression/0/body/5/3":
          {
            content: {
              en: "<expression/> Accept",
            },
            hash: "cb8cc171690ae20718de62786eb9528a",
          },
      },
    },
    "app/dashboard/personal/page.tsx": {
      entries: {
        "5/declaration/body/0/handler/body/1/argument/1/1": {
          content: {
            de: "Etwas ist schiefgelaufen",
            en: "Something went wrong",
            fr: "Une erreur s'est produite",
            nl: "Er is iets misgegaan",
          },
          hash: "a3cd2f01c073f1f5ff436d4b132d39cf",
        },
        "5/declaration/body/0/handler/body/1/argument/1/3": {
          content: {
            de: "Wir konnten Ihre Dashboard-Daten nicht laden. Bitte versuchen Sie es später erneut.",
            en: "We couldn't load your dashboard data. Please try again later.",
            fr: "Nous n'avons pas pu charger les données de votre tableau de bord. Veuillez réessayer plus tard.",
            nl: "We konden je dashboardgegevens niet laden. Probeer het later opnieuw.",
          },
          hash: "e7227dcc47baae989989483aeb4b145b",
        },
      },
    },
    "app/dashboard/settings/page.tsx": {
      entries: {
        "19/declaration/body/18/argument/1/1": {
          content: {
            en: "Settings",
          },
          hash: "8df6777277469c1fd88cc18dde2f1cc3",
        },
        "19/declaration/body/18/argument/1/3": {
          content: {
            en: "Manage your account settings and preferences",
          },
          hash: "f675eaf785dbfe9c02a8121df00dc0c0",
        },
        "19/declaration/body/18/argument/3/1/1/1/1": {
          content: {
            en: "<element:User></element:User> Profile",
          },
          hash: "7d39de18aa731a460d860daa93792aee",
        },
        "19/declaration/body/18/argument/3/1/1/1/3": {
          content: {
            en: "<element:Globe></element:Globe> Preferences",
          },
          hash: "4de94feb53a1f7b09c92364f45dc809c",
        },
        "19/declaration/body/18/argument/3/1/1/1/5": {
          content: {
            en: "<element:Bell></element:Bell> Notifications",
          },
          hash: "b7cd3fd88b25e32d5036805eb352bfb0",
        },
        "19/declaration/body/18/argument/3/1/1/1/7": {
          content: {
            en: "<element:Shield></element:Shield> Security",
          },
          hash: "a6b97191bb61478dcdd3d05c7ea04be6",
        },
        "19/declaration/body/18/argument/3/1/1/1/9": {
          content: {
            en: "<element:HelpCircle></element:HelpCircle> Help & Support",
          },
          hash: "a9f513c6f07f810f94bd4babadbf8ab0",
        },
        "19/declaration/body/18/argument/3/3/1/1/1/1": {
          content: {
            en: "Profile",
          },
          hash: "d7878693f91303a438852d617f6d35df",
        },
        "19/declaration/body/18/argument/3/3/1/1/1/3": {
          content: {
            en: "Manage your personal information",
          },
          hash: "6c5bec4ad3e76fe061bc889efd93ae58",
        },
        "19/declaration/body/18/argument/3/3/1/1/3/1/1/1/1": {
          content: {
            en: "Avatar",
          },
          hash: "cdf6b849d70e5737be62a71711799033",
        },
        "19/declaration/body/18/argument/3/3/1/1/3/1/1/1/3/1/1/1-alt": {
          content: {
            en: "Profile picture",
          },
          hash: "d9b5e595877ffe2557486a987dd28274",
        },
        "19/declaration/body/18/argument/3/3/1/1/3/1/1/1/3/3/3": {
          content: {
            en: "JPG, PNG up to 5MB",
          },
          hash: "77b8933ce22586bfcd4b6e337a72494c",
        },
        "19/declaration/body/18/argument/3/3/1/1/3/1/3/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Full Name",
            },
            hash: "ba3094a10db2bb9234cb7e0dc86e40fd",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/3/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "Your name",
            },
            hash: "b00c01deec0af9a441331a5134210de1",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Email",
            },
            hash: "e7f34943a0c2fb849db1839ff6ef5cb5",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/5/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              en: "Your email",
            },
            hash: "ed269a50d6b679a692b68608e66b64ab",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/5/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Contact support to change your email address",
            },
            hash: "410eb27ef4fd1bc645f157c8bbd2750c",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/7/openingElement/2/value/expression/body/1":
          {
            content: {
              en: "Default Currency",
            },
            hash: "3bff41a88a6d033a292fed1d90b47d01",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/7/openingElement/2/value/expression/body/3/1/1/1-placeholder":
          {
            content: {
              en: "Select currency",
            },
            hash: "a7604892fc4bfe50ead2ba5d5dd95c9c",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/7/openingElement/2/value/expression/body/3/3/1":
          {
            content: {
              en: "USD ($)",
            },
            hash: "70d998854c94afcefc169b57e0b880cc",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/7/openingElement/2/value/expression/body/3/3/3":
          {
            content: {
              en: "EUR (€)",
            },
            hash: "b6e2713de3d3bd0f30508582bb5e82df",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/7/openingElement/2/value/expression/body/3/3/5":
          {
            content: {
              en: "GBP (£)",
            },
            hash: "f972658cf8cff55ad6c55801e1bb7bef",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/7/openingElement/2/value/expression/body/3/3/7":
          {
            content: {
              en: "JPY (¥)",
            },
            hash: "92e310366597441706d9a62811db554e",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/7/openingElement/2/value/expression/body/3/3/9":
          {
            content: {
              en: "CAD ($)",
            },
            hash: "e76b742cccb277d4580ab4bc3a4a135d",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/7/openingElement/2/value/expression/body/5":
          {
            content: {
              en: "Used for displaying amounts throughout the app",
            },
            hash: "4eca49066da8c3d925e97cdf96d522d7",
          },
        "19/declaration/body/18/argument/3/3/1/1/3/1/9": {
          content: {
            en: "<element:Save></element:Save> Save Changes",
          },
          hash: "3a4d039386d28f188acf88133260c062",
        },
        "19/declaration/body/18/argument/3/3/1/3/1/1": {
          content: {
            en: "Preferences",
          },
          hash: "e3f4cfb8373d2b888a0c38e743de0e19",
        },
        "19/declaration/body/18/argument/3/3/1/3/1/3": {
          content: {
            en: "Customize your app experience",
          },
          hash: "0f991177915a79a0fff54212109ecb31",
        },
        "19/declaration/body/18/argument/3/3/1/3/3/1/1/1": {
          content: {
            en: "Dark Mode",
          },
          hash: "c81aa352f80642b6d57d1d52bcf5c2a2",
        },
        "19/declaration/body/18/argument/3/3/1/3/3/1/1/3": {
          content: {
            en: "Switch between light and dark themes",
          },
          hash: "32bff7d5bc5a042839de3f0b58f97dd2",
        },
        "19/declaration/body/18/argument/3/3/1/3/3/1/3/openingElement/2/value/expression/body/1/1/3":
          {
            content: {
              en: "Dark Mode",
            },
            hash: "6ba43197135868724dcaa53f79e66c97",
          },
        "19/declaration/body/18/argument/3/3/1/3/3/3/1/1": {
          content: {
            en: "Notifications",
          },
          hash: "c52df856139b50dbb1cae7bfb1cf73bb",
        },
        "19/declaration/body/18/argument/3/3/1/3/3/3/1/3": {
          content: {
            en: "Receive alerts about your account activity",
          },
          hash: "669bc653afdc50d934d571876a915973",
        },
        "19/declaration/body/18/argument/3/3/1/3/3/3/3/openingElement/2/value/expression/body/1/1/3":
          {
            content: {
              en: "Enable Notifications",
            },
            hash: "64a96bd4a1fd88b5687f6cffdcfc7f33",
          },
        "19/declaration/body/18/argument/3/3/1/3/3/5": {
          content: {
            en: "<element:Save></element:Save> Save Preferences",
          },
          hash: "10b89c7521732dfa633d3d48e8cbcac1",
        },
        "19/declaration/body/18/argument/3/3/1/5/1/1": {
          content: {
            en: "Account Actions",
          },
          hash: "d958ebff5ea455a05327d0412c4fa94f",
        },
        "19/declaration/body/18/argument/3/3/1/5/1/3": {
          content: {
            en: "Manage your account",
          },
          hash: "5a4c99112736d0c927001fb3cb886e34",
        },
        "19/declaration/body/18/argument/3/3/1/5/3/1/1": {
          content: {
            en: "<element:LogOut></element:LogOut> Sign Out",
          },
          hash: "9168f418bbdac7d58efee234f078b8d3",
        },
        "19/declaration/body/18/argument/3/3/1/5/3/1/3": {
          content: {
            en: "<element:Trash2></element:Trash2> Delete Account",
          },
          hash: "4e3387ead0b0255246cf8fe496fa567c",
        },
        "19/declaration/body/18/argument/3/3/1/5/3/3/1/3/1": {
          content: {
            en: "Warning",
          },
          hash: "6618da2c7e5e93bb4ea0e16d29ab8c4c",
        },
        "19/declaration/body/18/argument/3/3/1/5/3/3/1/3/3": {
          content: {
            en: "Deleting your account will permanently remove all your data, including transactions, budgets, and goals. This action cannot be undone.",
          },
          hash: "30cdc830023282d93ddc233bdb993411",
        },
      },
    },
    "app/dashboard/subscriptions/page.tsx": {
      entries: {
        "14/declaration/body/19/argument/11/1/3-placeholder": {
          content: {
            en: "Search subscriptions...",
          },
          hash: "57054d64b70d78c932009cc198e747e4",
        },
        "14/declaration/body/19/argument/11/5/3/1/expression/0/body": {
          content: {
            en: "Sort by {option.label}",
          },
          hash: "9ab97da0073e1e40c6ea6f51a932dcd3",
        },
        "14/declaration/body/19/argument/15/expression/alternate/consequent/1/7/expression/right":
          {
            content: {
              en: "<element:Plus></element:Plus> Add Your First Subscription",
            },
            hash: "24cd35adb1284d4dc528ca88d8631e2f",
          },
        "14/declaration/body/19/argument/3/1/1": {
          content: {
            en: "Subscriptions",
          },
          hash: "0d17f4d8d660df40b41d82e0171af6ad",
        },
        "14/declaration/body/19/argument/3/1/3": {
          content: {
            en: "Manage your recurring payments and bills",
          },
          hash: "e65c4300678e6ea619af59dcc4f5ebe1",
        },
        "14/declaration/body/19/argument/3/3": {
          content: {
            en: "<element:Plus></element:Plus> Add Subscription",
          },
          hash: "e0b79335c331d5417d19d64b0bfb0fbf",
        },
        "14/declaration/body/19/argument/7/expression/right/1/1/1/3/3": {
          content: {
            en: "Monthly Total",
          },
          hash: "e98b7e457d31ce2bd3eceb417a65c7a0",
        },
        "14/declaration/body/19/argument/7/expression/right/3/1/1/3/3": {
          content: {
            en: "Active",
          },
          hash: "3e1ec025c4a50830bbb9ad57a176630a",
        },
        "14/declaration/body/19/argument/7/expression/right/5/1/1/3/3": {
          content: {
            en: "Due This Week",
          },
          hash: "f0cb6d8d94d4496308514d8807df14de",
        },
        "14/declaration/body/19/argument/7/expression/right/7/1/1/3/3": {
          content: {
            en: "Expiring Soon",
          },
          hash: "0e149c65f320079c950db404558f6a4b",
        },
      },
    },
    "app/dashboard/transactions/page.tsx": {
      entries: {
        "26/declaration/body/19/consequent/0/argument/1/1": {
          content: {
            en: "Loading transactions...",
          },
          hash: "ef095462e19def338a51a5b15d18162e",
        },
        "26/declaration/body/19/consequent/0/argument/1/3": {
          content: {
            en: "Please wait while we load your transactions.",
          },
          hash: "bc7f7d5bec2bf73eaa03adf2e62a1b66",
        },
        "26/declaration/body/20/argument/1": {
          content: {
            en: "Transactions",
          },
          hash: "59973447457c6bd57c161d594f551651",
        },
        "26/declaration/body/20/argument/3/1/3/1": {
          content: {
            en: "Total Income",
          },
          hash: "d96c55cd509ed55f6dfbf78dbcf0605d",
        },
        "26/declaration/body/20/argument/3/1/5/3": {
          content: {
            en: "Total income received",
          },
          hash: "0d2e0b3f363cf33f5c5cf05e4055f789",
        },
        "26/declaration/body/20/argument/3/3/3/1": {
          content: {
            en: "Total Expenses",
          },
          hash: "57bfc5bd4e97342446e0423f6a9c0fd0",
        },
        "26/declaration/body/20/argument/3/3/5/3": {
          content: {
            en: "Total expenses paid",
          },
          hash: "4f063107dc23f2d7039b39a955e83f87",
        },
        "26/declaration/body/20/argument/3/5/3/1": {
          content: {
            en: "Net Balance",
          },
          hash: "3184dabbd47f9c11266edb402756e0e8",
        },
        "26/declaration/body/20/argument/3/5/5/3": {
          content: {
            en: "Current account balance",
          },
          hash: "bd7332edd4ad8b87f0ba9662e8a8140e",
        },
        "26/declaration/body/20/argument/5/1": {
          content: {
            en: "Transactions",
          },
          hash: "59973447457c6bd57c161d594f551651",
        },
        "26/declaration/body/20/argument/5/3/1/1": {
          content: {
            en: "<element:PlusCircle></element:PlusCircle> Add New Transaction",
          },
          hash: "cc624f8100d81015ca1519b24b73b5c6",
        },
        "26/declaration/body/20/argument/5/3/3/1/1": {
          content: {
            en: "<expression/> Transaction",
          },
          hash: "156c267d0747b958ad7a13c0e0435c65",
        },
        "26/declaration/body/20/argument/5/3/3/3/1/1/1": {
          content: {
            en: "Description",
          },
          hash: "e17686a22ffad04cc7bb70524ed4478b",
        },
        "26/declaration/body/20/argument/5/3/3/3/1/3/1": {
          content: {
            en: "Amount",
          },
          hash: "a0ae9c6ff98b57475cdce56f66066aa4",
        },
        "26/declaration/body/20/argument/5/3/3/3/3/1/1": {
          content: {
            en: "Type",
          },
          hash: "f04471a7ddac844b9ad145eb9911ef75",
        },
        "26/declaration/body/20/argument/5/3/3/3/3/1/3/openingElement/2/value/expression/body/1/1-placeholder":
          {
            content: {
              en: "Select type",
            },
            hash: "fa373e47f55ff081982844a853be3a88",
          },
        "26/declaration/body/20/argument/5/3/3/3/3/1/3/openingElement/2/value/expression/body/3/1":
          {
            content: {
              en: "Income",
            },
            hash: "398bff8476817f942c70c4aadad5dd91",
          },
        "26/declaration/body/20/argument/5/3/3/3/3/1/3/openingElement/2/value/expression/body/3/3":
          {
            content: {
              en: "Expense",
            },
            hash: "5d71ac3af27428af3058019d1cbfcc61",
          },
        "26/declaration/body/20/argument/5/3/3/3/3/3/1": {
          content: {
            en: "Date",
          },
          hash: "56f41c5d30a76295bb087b20b7bee4c3",
        },
        "26/declaration/body/20/argument/5/3/3/3/5/1/1": {
          content: {
            en: "Account",
          },
          hash: "01215c12fb1cdb93bd0c84c1382bef56",
        },
        "26/declaration/body/20/argument/5/3/3/3/5/1/3/openingElement/2/value/expression/body/1/1-placeholder":
          {
            content: {
              en: "Select account",
            },
            hash: "1dbc9f1c405b721f8d44e25865e49128",
          },
        "26/declaration/body/20/argument/5/3/3/3/5/3/1": {
          content: {
            en: "Category (Optional)",
          },
          hash: "bff671a5e11b4d8e264ba8deef37d8ee",
        },
        "26/declaration/body/20/argument/5/3/3/3/5/3/3/openingElement/2/value/expression/body/1/1-placeholder":
          {
            content: {
              en: "Select category",
            },
            hash: "48c701bd440d494da6a6fac2570e7196",
          },
        "26/declaration/body/20/argument/5/3/3/3/5/3/3/openingElement/2/value/expression/body/3/1":
          {
            content: {
              en: "Uncategorized",
            },
            hash: "fe9a031744fb6cd3488d83b818a8722e",
          },
        "26/declaration/body/20/argument/5/3/3/3/7/1": {
          content: {
            en: "Currency",
          },
          hash: "d2ba9a9cea418ea653e3ca6785617a3f",
        },
        "26/declaration/body/20/argument/5/3/3/3/7/3/openingElement/2/value/expression/body/1/1-placeholder":
          {
            content: {
              en: "Select currency",
            },
            hash: "a7604892fc4bfe50ead2ba5d5dd95c9c",
          },
        "26/declaration/body/20/argument/5/3/3/3/7/3/openingElement/2/value/expression/body/3/1":
          {
            content: {
              en: "USD - US Dollar",
            },
            hash: "1607084f8e2559499437984ca4ef026b",
          },
        "26/declaration/body/20/argument/5/3/3/3/7/3/openingElement/2/value/expression/body/3/11":
          {
            content: {
              en: "AUD - Australian Dollar",
            },
            hash: "0bd1087a85f2fe8b248880dfcab6c8d6",
          },
        "26/declaration/body/20/argument/5/3/3/3/7/3/openingElement/2/value/expression/body/3/13":
          {
            content: {
              en: "CHF - Swiss Franc",
            },
            hash: "e3259501d1a854facde7dab60dc26ca1",
          },
        "26/declaration/body/20/argument/5/3/3/3/7/3/openingElement/2/value/expression/body/3/15":
          {
            content: {
              en: "CNY - Chinese Yuan",
            },
            hash: "cd5364f7024a7bd3efcb92e215a0a4f8",
          },
        "26/declaration/body/20/argument/5/3/3/3/7/3/openingElement/2/value/expression/body/3/17":
          {
            content: {
              en: "INR - Indian Rupee",
            },
            hash: "b4500d1ed9055e36bdc5dad5a292e3e4",
          },
        "26/declaration/body/20/argument/5/3/3/3/7/3/openingElement/2/value/expression/body/3/19":
          {
            content: {
              en: "BRL - Brazilian Real",
            },
            hash: "82d2979f60f0d0835e0bc8f78b75eddb",
          },
        "26/declaration/body/20/argument/5/3/3/3/7/3/openingElement/2/value/expression/body/3/3":
          {
            content: {
              en: "EUR - Euro",
            },
            hash: "5d10f8c89e38131a0fca646a2cef2918",
          },
        "26/declaration/body/20/argument/5/3/3/3/7/3/openingElement/2/value/expression/body/3/5":
          {
            content: {
              en: "GBP - British Pound",
            },
            hash: "e210ed72e17f76dd537a247204fa419f",
          },
        "26/declaration/body/20/argument/5/3/3/3/7/3/openingElement/2/value/expression/body/3/7":
          {
            content: {
              en: "JPY - Japanese Yen",
            },
            hash: "4c6460f5b256f466be5ed4fffd31b82c",
          },
        "26/declaration/body/20/argument/5/3/3/3/7/3/openingElement/2/value/expression/body/3/9":
          {
            content: {
              en: "CAD - Canadian Dollar",
            },
            hash: "8609a98f113302283dd70d21b09e3474",
          },
        "26/declaration/body/20/argument/5/3/3/3/9/1/1": {
          content: {
            en: "Cancel",
          },
          hash: "643431b4a45271ffb02d2957be7884b3",
        },
        "26/declaration/body/20/argument/7/1/1": {
          content: {
            en: "Recent Transactions",
          },
          hash: "c1adc026ca419c2e105271ce9155f17a",
        },
        "26/declaration/body/20/argument/7/3/1/1/1/1/1": {
          content: {
            en: "Date",
          },
          hash: "56f41c5d30a76295bb087b20b7bee4c3",
        },
        "26/declaration/body/20/argument/7/3/1/1/1/1/11": {
          content: {
            en: "Account",
          },
          hash: "01215c12fb1cdb93bd0c84c1382bef56",
        },
        "26/declaration/body/20/argument/7/3/1/1/1/1/13": {
          content: {
            en: "Actions",
          },
          hash: "c46571856723b03262fd33f511116298",
        },
        "26/declaration/body/20/argument/7/3/1/1/1/1/3": {
          content: {
            en: "Description",
          },
          hash: "e17686a22ffad04cc7bb70524ed4478b",
        },
        "26/declaration/body/20/argument/7/3/1/1/1/1/5": {
          content: {
            en: "Amount",
          },
          hash: "a0ae9c6ff98b57475cdce56f66066aa4",
        },
        "26/declaration/body/20/argument/7/3/1/1/1/1/7": {
          content: {
            en: "Type",
          },
          hash: "f04471a7ddac844b9ad145eb9911ef75",
        },
        "26/declaration/body/20/argument/7/3/1/1/1/1/9": {
          content: {
            en: "Category",
          },
          hash: "1b0340cd175aa5a2be745a0d54908466",
        },
        "26/declaration/body/20/argument/7/3/1/1/3/1/expression/alternate/0/body/1/argument/11/3/3/1/1":
          {
            content: {
              en: "Delete Transaction",
            },
            hash: "87b4d0950b2711809dc92b97acb459c7",
          },
        "26/declaration/body/20/argument/7/3/1/1/3/1/expression/alternate/0/body/1/argument/11/3/3/1/3":
          {
            content: {
              en: "Are you sure you want to delete this transaction? This action cannot be undone.",
            },
            hash: "52534ba415597b3e6aefdf568701912f",
          },
        "26/declaration/body/20/argument/7/3/1/1/3/1/expression/alternate/0/body/1/argument/11/3/3/3/1/1":
          {
            content: {
              en: "Cancel",
            },
            hash: "2e2a849c2223911717de8caa2c71bade",
          },
        "26/declaration/body/20/argument/7/3/1/1/3/1/expression/alternate/0/body/1/argument/11/3/3/3/3":
          {
            content: {
              en: "Delete",
            },
            hash: "57e524cb226075959bda5325cf3e6af3",
          },
        "26/declaration/body/20/argument/7/3/1/1/3/1/expression/consequent/1": {
          content: {
            en: "No transactions found. Add one to get started.",
          },
          hash: "4642aec5e61a9ea5f9f29d9a88cc478e",
        },
      },
    },
    "app/invite/[token]/page.tsx": {
      entries: {
        "10/declaration/body/12/consequent/0/argument/1/3": {
          content: {
            en: "Loading invitation...",
          },
          hash: "adf097aae24e3295f87d98ce0c69ca50",
        },
        "10/declaration/body/13/consequent/0/argument/1/1/1": {
          content: {
            en: "<element:AlertCircle></element:AlertCircle> Invalid Invitation",
          },
          hash: "8055a2413716ea7008f116c4b0eff01c",
        },
        "10/declaration/body/13/consequent/0/argument/1/3/3": {
          content: {
            en: "Go to Dashboard",
          },
          hash: "eaaf45d988e7c6b07ee322321a3b85ab",
        },
        "10/declaration/body/14/consequent/0/argument/1/1/1": {
          content: {
            en: "<element:Mail></element:Mail> Sign In Required",
          },
          hash: "cda1033f105545543c3554d532d21688",
        },
        "10/declaration/body/14/consequent/0/argument/1/1/3": {
          content: {
            en: "You need to sign in to accept this invitation.",
          },
          hash: "6c71e3c247c2e39ed08601127f85b21d",
        },
        "10/declaration/body/14/consequent/0/argument/1/3/1/1": {
          content: {
            en: "You've been invited to join <element:strong><expression/></element:strong>.",
          },
          hash: "045269a7b06a98062fd6e98a2a27f5a8",
        },
        "10/declaration/body/14/consequent/0/argument/1/3/1/3/1": {
          content: {
            en: "Sign In",
          },
          hash: "a07ab477f26ae387ec241dc6eb29f7fb",
        },
        "10/declaration/body/14/consequent/0/argument/1/3/1/3/3": {
          content: {
            en: "Create Account",
          },
          hash: "ac5c5c0dc5084593cad54581958c7f37",
        },
        "10/declaration/body/18/argument/1/1/1": {
          content: {
            en: "<element:Users></element:Users> Family Group Invitation",
          },
          hash: "59365ad3c3e832fd063be3b5f5ee1ab4",
        },
        "10/declaration/body/18/argument/1/1/3": {
          content: {
            en: "You've been invited to join a family group",
          },
          hash: "df2f251f111d890b993c2304475b3e43",
        },
        "10/declaration/body/18/argument/1/3/1/expression/right/3": {
          content: {
            en: "Invitation Unavailable",
          },
          hash: "c3a72c0ec8d4200b84eb11cf7bbe7188",
        },
        "10/declaration/body/18/argument/1/3/3/1/3/expression/right": {
          content: {
            en: "Invited by: {invitation.invited_by_email}",
          },
          hash: "054a124e1e887a941c82a3cf2d56ed7f",
        },
        "10/declaration/body/18/argument/1/3/3/1/5/expression/right": {
          content: {
            en: "Invited on: <function:format/>",
          },
          hash: "714155835865528c8c0a536e4fbffff3",
        },
        "10/declaration/body/18/argument/1/3/3/1/7/expression/right": {
          content: {
            en: "Expires: <function:format/>",
          },
          hash: "8f83efa2ca28d6c251c4444c8cc0fae3",
        },
        "10/declaration/body/18/argument/1/3/3/3/1/1": {
          content: {
            en: "Your email:",
          },
          hash: "e14a74269b6fc67b90c9096317fb249f",
        },
        "10/declaration/body/18/argument/1/3/3/3/3/1": {
          content: {
            en: "Invited email:",
          },
          hash: "236dd2d0fd3337f5f42bea575a43bef0",
        },
        "10/declaration/body/18/argument/1/3/3/3/5/expression/right/3": {
          content: {
            en: "This invitation was sent to a different email address. You can still accept it if you have access to that email.",
          },
          hash: "947b8b6e6915bb5a6631b18f5fd5f6cb",
        },
        "10/declaration/body/18/argument/1/3/5/expression/right/1": {
          content: {
            en: "<expression/> Decline",
          },
          hash: "3a4648c030679d5f2922fbb0b1f93552",
        },
        "10/declaration/body/18/argument/1/3/5/expression/right/3": {
          content: {
            en: "<expression/> Accept Invitation",
          },
          hash: "59a469cfcd0741998de2b72d7b1fe4fa",
        },
        "10/declaration/body/18/argument/1/3/7/expression/right": {
          content: {
            en: "Go to Dashboard",
          },
          hash: "eaaf45d988e7c6b07ee322321a3b85ab",
        },
      },
    },
    "app/page.tsx": {
      entries: {
        "18/declaration/body/7/argument/11/1/3": {
          content: {
            de: "© 2024 Budget Tracker. Alle Rechte vorbehalten.",
            en: "© 2024 Budget Tracker. All rights reserved.",
            fr: "© 2024 Budget Tracker. Tous droits réservés.",
            nl: "© 2024 Budget Tracker. Alle rechten voorbehouden.",
          },
          hash: "ad60742e087f4d28b8a213d1dc450a4b",
        },
        "18/declaration/body/7/argument/11/1/5": {
          content: {
            de: "Mit ❤️ erstellt für besseres finanzielles Wohlbefinden",
            en: "Built with ❤️ for better financial wellness",
            fr: "Conçu avec ❤️ pour un meilleur bien-être financier",
            nl: "Gebouwd met ❤️ voor beter financieel welzijn",
          },
          hash: "afad03d62d7e05b34f6cb823830a1bd5",
        },
        "18/declaration/body/7/argument/3/5/3/11/1/1/3/1": {
          content: {
            de: "Nehmen Sie an unserem Beta-Programm teil",
            en: "Join Our Beta Program",
            fr: "Rejoignez notre programme bêta",
            nl: "Doe mee aan ons bètaprogramma",
          },
          hash: "bd22855f186a09a0541b07c98144bbbb",
        },
        "18/declaration/body/7/argument/3/5/3/11/1/1/3/3": {
          content: {
            de: "Erhalten Sie exklusiven frühen Zugang zu revolutionären Funktionen",
            en: "Get exclusive early access to revolutionary features",
            fr: "Obtenez un accès anticipé exclusif à des fonctionnalités révolutionnaires",
            nl: "Krijg exclusieve vroege toegang tot revolutionaire functies",
          },
          hash: "1ffb0e34641a6b86fdcc7cfe34393fd8",
        },
        "18/declaration/body/7/argument/3/5/3/11/1/3/1": {
          content: {
            de: "Mehr erfahren",
            en: "Learn More",
            fr: "En savoir plus",
            nl: "Meer informatie",
          },
          hash: "364568235b9b8a1064e2a76e2d8a67cc",
        },
        "18/declaration/body/7/argument/3/5/3/3/1": {
          content: {
            de: "Übernehmen Sie die Kontrolle über Ihre <element:span>Finanzen</element:span>",
            en: "Take Control of Your <element:span>Finances</element:span>",
            fr: "Prenez le contrôle de vos <element:span>finances</element:span>",
            nl: "Neem controle over je <element:span>financiën</element:span>",
          },
          hash: "cb795cdf3fe80f742eb171c1d61a63a8",
        },
        "18/declaration/body/7/argument/3/5/3/3/3": {
          content: {
            de: "Verfolgen Sie Ausgaben, setzen Sie Budgets und erreichen Sie Ihre finanziellen Ziele mit unserer <element:span>leistungsstarken</element:span> und <element:span>intuitiven</element:span> Budget-Tracking-Anwendung.",
            en: "Track expenses, set budgets, and achieve your financial goals with our <element:span>powerful</element:span>  and <element:span>intuitive</element:span>  budget tracking application.",
            fr: "Suivez vos dépenses, définissez des budgets et atteignez vos objectifs financiers avec notre application de suivi budgétaire <element:span>puissante</element:span> et <element:span>intuitive</element:span>.",
            nl: "Volg uitgaven, stel budgetten in en bereik je financiële doelen met onze <element:span>krachtige</element:span> en <element:span>intuïtieve</element:span> budgettrackingapplicatie.",
          },
          hash: "292a0cbaa3f009f5dc399ecba0561302",
        },
        "18/declaration/body/7/argument/3/5/3/7/1/3": {
          content: {
            de: "Intelligente Analysen",
            en: "Smart Analytics",
            fr: "Analyses intelligentes",
            nl: "Slimme analyses",
          },
          hash: "75e4ce71d87ebee5b87d00eb8c2dafb4",
        },
        "18/declaration/body/7/argument/3/5/3/7/3/3": {
          content: {
            de: "Bankniveau-Sicherheit",
            en: "Bank-level Security",
            fr: "Sécurité de niveau bancaire",
            nl: "Bankwaardige beveiliging",
          },
          hash: "ee59a8ed028ba038bdf0875174f63748",
        },
        "18/declaration/body/7/argument/3/5/3/7/5/3": {
          content: {
            de: "Familienfreigabe",
            en: "Family Sharing",
            fr: "Partage familial",
            nl: "Gezinsdeling",
          },
          hash: "a7b418bd03d5a9b019cf97427b0646df",
        },
        "18/declaration/body/7/argument/3/5/7/1/1/3": {
          content: {
            de: "Starten Sie Ihre Reise",
            en: "Start Your Journey",
            fr: "Commencez votre parcours",
            nl: "Begin je reis",
          },
          hash: "a68dd6567d4691ce1fc8741fe7dbdf24",
        },
        "18/declaration/body/7/argument/3/5/7/1/1/5": {
          content: {
            de: "Schließen Sie sich tausenden an, die ihre Finanzen besser verwalten",
            en: "Join thousands managing their finances better",
            fr: "Rejoignez des milliers de personnes qui gèrent mieux leurs finances",
            nl: "Sluit je aan bij duizenden die hun financiën beter beheren",
          },
          hash: "3172ca01bb35580553604443acd149fb",
        },
        "18/declaration/body/7/argument/3/5/7/1/3/1/1/1": {
          content: {
            de: "Registrieren",
            en: "Sign Up",
            fr: "S'inscrire",
            nl: "Aanmelden",
          },
          hash: "f229fbecc4f27e5b7824c12950febbc4",
        },
        "18/declaration/body/7/argument/3/5/7/1/3/1/1/3": {
          content: {
            de: "Anmelden",
            en: "Login",
            fr: "Connexion",
            nl: "Inloggen",
          },
          hash: "cc1f6b029f70da1b58f16eb1ffdae029",
        },
        "18/declaration/body/7/argument/3/5/7/1/3/1/3/1/1/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              de: "Vollständiger Name",
              en: "Full Name",
              fr: "Nom complet",
              nl: "Volledige naam",
            },
            hash: "ddddadd4f8b1a46102a798c3ad6fe180",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/3/1/1/1/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              de: "John Doe",
              en: "John Doe",
              fr: "John Doe",
              nl: "John Doe",
            },
            hash: "febee8e9ab40b2fe5106d72675228d00",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/3/1/1/1/3/openingElement/2/value/expression/body/1":
          {
            content: {
              de: "Benutzername",
              en: "Username",
              fr: "Nom d'utilisateur",
              nl: "Gebruikersnaam",
            },
            hash: "4b3a309ae7914cfed7c55d5e9c09327a",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/3/1/1/1/3/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              de: "johndoe123",
              en: "johndoe123",
              fr: "johndoe123",
              nl: "johndoe123",
            },
            hash: "af03b2ae170971a0dd6758a962add10b",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/3/1/1/1/5/openingElement/2/value/expression/body/1":
          {
            content: {
              de: "E-Mail",
              en: "Email",
              fr: "E-mail",
              nl: "E-mail",
            },
            hash: "7c4d5b0ca4887fd7aa3ee0f12d1273a7",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/3/1/1/1/5/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              de: "<EMAIL>",
              en: "<EMAIL>",
              fr: "<EMAIL>",
              nl: "<EMAIL>",
            },
            hash: "b13d316cc2609fffd0739c45190b6787",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/3/1/1/1/7/openingElement/2/value/expression/body/1":
          {
            content: {
              de: "Passwort",
              en: "Password",
              fr: "Mot de passe",
              nl: "Wachtwoord",
            },
            hash: "82860e347783aa3ed7836f0f0e37c86b",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/3/1/1/1/7/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              de: "••••••••",
              en: "••••••••",
              fr: "••••••••",
              nl: "••••••••",
            },
            hash: "5ee7cd1a4a7fe6fb896e8a0c4b2d7255",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/3/1/1/1/7/openingElement/2/value/expression/body/5":
          {
            content: {
              de: "Mind. 8 Zeichen mit Groß- und Kleinbuchstaben & Zahlen",
              en: "8+ characters with uppercase, lowercase & number",
              fr: "8+ caractères avec majuscules, minuscules & chiffres",
              nl: "8+ tekens met hoofdletters, kleine letters & cijfers",
            },
            hash: "71eb684fe756d7e05c1f63710498a00a",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/3/1/1/3": {
          content: {
            de: "<expression/> Konto erstellen",
            en: "<expression/> Create Account",
            fr: "<expression/> Créer un compte",
            nl: "<expression/> Account aanmaken",
          },
          hash: "6a91dc02b3c6ee2871a90e32f440e314",
        },
        "18/declaration/body/7/argument/3/5/7/1/3/1/5/1/1/1/openingElement/2/value/expression/body/1":
          {
            content: {
              de: "E-Mail oder Benutzername",
              en: "Email or Username",
              fr: "E-mail ou nom d'utilisateur",
              nl: "E-mail of gebruikersnaam",
            },
            hash: "e0a3f6804f0a42267bb212b715258693",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/5/1/1/1/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              de: "<EMAIL> oder johndoe123",
              en: "<EMAIL> or johndoe123",
              fr: "<EMAIL> ou johndoe123",
              nl: "<EMAIL> of johndoe123",
            },
            hash: "a279a308b393261f7411d17eb3f50f53",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/5/1/1/3/openingElement/2/value/expression/body/1":
          {
            content: {
              de: "Passwort",
              en: "Password",
              fr: "Mot de passe",
              nl: "Wachtwoord",
            },
            hash: "b4a56590dcbd7c353245c6f23fc5ef94",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/5/1/1/3/openingElement/2/value/expression/body/3/1-placeholder":
          {
            content: {
              de: "••••••••",
              en: "••••••••",
              fr: "••••••••",
              nl: "••••••••",
            },
            hash: "5ee7cd1a4a7fe6fb896e8a0c4b2d7255",
          },
        "18/declaration/body/7/argument/3/5/7/1/3/1/5/1/1/5/1": {
          content: {
            de: "Passwort vergessen?",
            en: "Forgot Password?",
            fr: "Mot de passe oublié ?",
            nl: "Wachtwoord vergeten?",
          },
          hash: "cb720d5893d1b002a0f767807dbff9d6",
        },
        "18/declaration/body/7/argument/3/5/7/1/3/1/5/1/1/7": {
          content: {
            de: "<expression/> Anmelden",
            en: "<expression/> Sign In",
            fr: "<expression/> Se connecter",
            nl: "<expression/> Inloggen",
          },
          hash: "a65c28d0d5fdafa89f328b5297d58554",
        },
        "18/declaration/body/7/argument/7/1/1/1": {
          content: {
            de: "Alles, was Sie brauchen, um <element:span>Ihr Geld zu verwalten</element:span>",
            en: "Everything you need to <element:span>manage your money</element:span>",
            fr: "Tout ce dont vous avez besoin pour <element:span>gérer votre argent</element:span>",
            nl: "Alles wat je nodig hebt om <element:span>je geld te beheren</element:span>",
          },
          hash: "bd5dac6ecdf70fea05a141b8ac0f731c",
        },
        "18/declaration/body/7/argument/7/1/1/3": {
          content: {
            de: "Leistungsstarke Tools, die entwickelt wurden, um die Finanzverwaltung einfach und effektiv zu gestalten",
            en: "Powerful tools designed to make financial management simple and effective",
            fr: "Des outils puissants conçus pour rendre la gestion financière simple et efficace",
            nl: "Krachtige tools ontworpen om financieel beheer eenvoudig en effectief te maken",
          },
          hash: "7b379d3e406f5383e5fce26ec8754a55",
        },
        "18/declaration/body/7/argument/7/1/3/1/3": {
          content: {
            de: "Detaillierte Analysen",
            en: "Detailed Analytics",
            fr: "Analyses détaillées",
            nl: "Gedetailleerde analyses",
          },
          hash: "efea7cce848e629c5983697d349e61d2",
        },
        "18/declaration/body/7/argument/7/1/3/1/5": {
          content: {
            de: "Visualisieren Sie Ihre Ausgabenmuster mit anschaulichen Diagrammen und umsetzbaren Erkenntnissen",
            en: "Visualize your spending patterns with beautiful charts and actionable insights",
            fr: "Visualisez vos habitudes de dépenses avec de beaux graphiques et des informations exploitables",
            nl: "Visualiseer je uitgavenpatronen met mooie grafieken en bruikbare inzichten",
          },
          hash: "f5f4c50c024bc51c5a505bdd32931e95",
        },
        "18/declaration/body/7/argument/7/1/3/3/3": {
          content: {
            de: "Sicher & Privat",
            en: "Secure & Private",
            fr: "Sécurisé & privé",
            nl: "Veilig & privé",
          },
          hash: "0e7bd2cbe4ec73f9b8c14cf48c4df572",
        },
        "18/declaration/body/7/argument/7/1/3/3/5": {
          content: {
            de: "Ihre Finanzdaten werden verschlüsselt und niemals an Dritte weitergegeben",
            en: "Your financial data is encrypted and never shared with third parties",
            fr: "Vos données financières sont cryptées et jamais partagées avec des tiers",
            nl: "Je financiële gegevens zijn versleuteld en worden nooit gedeeld met derden",
          },
          hash: "4fae24460349ad03dae415da7c3f5797",
        },
        "18/declaration/body/7/argument/7/1/3/5/3": {
          content: {
            de: "Überall zugreifen",
            en: "Access Anywhere",
            fr: "Accès partout",
            nl: "Overal toegang",
          },
          hash: "1613a5771394e1567d33e7f00cd21726",
        },
        "18/declaration/body/7/argument/7/1/3/5/5": {
          content: {
            de: "Verfolgen Sie Ihre Finanzen auf jedem Gerät mit unserem responsiven Design",
            en: "Track your finances on any device with our responsive design",
            fr: "Suivez vos finances sur n'importe quel appareil grâce à notre design responsive",
            nl: "Volg je financiën op elk apparaat met ons responsieve ontwerp",
          },
          hash: "6ba4c4fcf1a9629824caa32a0ede4dcc",
        },
      },
    },
    "beta/components/BetaDashboardHeader.tsx": {
      entries: {
        "15/declaration/body/8/argument/1/3/3/1/1": {
          content: {
            en: "BT",
          },
          hash: "b11b5c1a2d5674b439048448bf1db3a5",
        },
        "15/declaration/body/8/argument/1/3/3/3": {
          content: {
            en: "Budget Tracker",
          },
          hash: "c970d03c09b541a19ea49ba831c210ee",
        },
        "15/declaration/body/8/argument/1/3/5/1": {
          content: {
            en: "<element:Sparkles></element:Sparkles> BETA",
          },
          hash: "2875edb8f30d3d9d8a85d3784f089859",
        },
        "15/declaration/body/8/argument/1/7/11/3/1/1/5/1": {
          content: {
            en: "Beta Member",
          },
          hash: "b6c86b6df207a05336468b840aa61214",
        },
        "15/declaration/body/8/argument/1/7/11/3/1/1/5/3": {
          content: {
            en: "• <expression/> feedback",
          },
          hash: "b445175ca0a9d161fa44f2dc8325de55",
        },
        "15/declaration/body/8/argument/1/7/11/3/17/3": {
          content: {
            en: "Log out",
          },
          hash: "9a236bb8f8bec867ec0d0950d38bcc71",
        },
        "15/declaration/body/8/argument/1/7/11/3/5/1/3": {
          content: {
            en: "Profile",
          },
          hash: "d7878693f91303a438852d617f6d35df",
        },
        "15/declaration/body/8/argument/1/7/11/3/7/1/3": {
          content: {
            en: "Settings",
          },
          hash: "8df6777277469c1fd88cc18dde2f1cc3",
        },
        "15/declaration/body/8/argument/1/7/11/3/9/3": {
          content: {
            en: "Reset Onboarding",
          },
          hash: "9742212daf8dc74538cef5c5486992f5",
        },
        "15/declaration/body/8/argument/1/7/3/openingElement/0/value/expression":
          {
            content: {
              en: "<element:MessageCircle></element:MessageCircle> Feedback",
            },
            hash: "9dbaa5dae75fff7a7c16ce4bc054e468",
          },
      },
    },
    "beta/components/BetaDashboardSidebar.tsx": {
      entries: {
        "11/declaration/body/4/argument/1/1/15/1": {
          content: {
            en: "Support",
          },
          hash: "29d4ec8c344929b4199d0ceb40cfa7d3",
        },
        "11/declaration/body/4/argument/1/1/15/3/1/1": {
          content: {
            en: "<element:Settings></element:Settings> Settings",
          },
          hash: "292ad169645e3d3c7195da7937aa0bd9",
        },
        "11/declaration/body/4/argument/1/1/15/3/3/1": {
          content: {
            en: "<element:HelpCircle></element:HelpCircle> Help & Feedback",
          },
          hash: "8a0e2c7dada61c8ed2f1a44f6bd707cc",
        },
        "11/declaration/body/4/argument/1/1/3/1": {
          content: {
            en: "Navigation",
          },
          hash: "2890c3f6bca0a32f046d0a5e7d752d98",
        },
        "11/declaration/body/4/argument/1/1/9/1/1": {
          content: {
            en: "Beta Features",
          },
          hash: "c12bede6c3351e865bc82a3c2ec89b14",
        },
        "11/declaration/body/4/argument/1/5/1/1/3": {
          content: {
            en: "Beta Program",
          },
          hash: "78a3aa92e7912945775a9ac7547f0980",
        },
        "11/declaration/body/4/argument/1/5/1/3": {
          content: {
            en: "You're helping shape the future of financial management",
          },
          hash: "9f83f31eb3489eee12aa1bbe26164f4f",
        },
        "11/declaration/body/4/argument/1/5/1/5/1": {
          content: {
            en: "View Beta Status",
          },
          hash: "c16c5d55b2601893f88c7a381952a659",
        },
      },
    },
    "features/dashboard/components/dashboard-client.tsx": {
      entries: {
        "18/declaration/body/6/argument/1/1": {
          content: {
            de: "Dashboard",
            en: "Dashboard",
            fr: "Tableau de bord",
            nl: "Dashboard",
          },
          hash: "c9380ea68c8c76ea451bd9613329a07c",
        },
        "18/declaration/body/6/argument/1/3": {
          content: {
            de: "Willkommen zurück! Hier ist Ihr finanzieller Überblick.",
            en: "Welcome back! Here's your financial overview.",
            fr: "Bon retour ! Voici votre aperçu financier.",
            nl: "Welkom terug! Hier is je financiële overzicht.",
          },
          hash: "9a65533a9375ec63742fc144419b8a42",
        },
        "18/declaration/body/6/argument/13/1/1/1": {
          content: {
            de: "Übersicht",
            en: "Overview",
            fr: "Aperçu",
            nl: "Overzicht",
          },
          hash: "30c54e4dc4ce599b87d94be34a8617f5",
        },
        "18/declaration/body/6/argument/13/1/1/3": {
          content: {
            de: "Transaktionen",
            en: "Transactions",
            fr: "Transactions",
            nl: "Transacties",
          },
          hash: "59973447457c6bd57c161d594f551651",
        },
        "18/declaration/body/6/argument/13/1/1/5": {
          content: {
            de: "Budgets",
            en: "Budgets",
            fr: "Budgets",
            nl: "Budgetten",
          },
          hash: "8dcb6cddfd7b9a0ce4a4e908b86662f7",
        },
        "18/declaration/body/6/argument/13/1/1/7": {
          content: {
            de: "Ziele",
            en: "Goals",
            fr: "Objectifs",
            nl: "Doelen",
          },
          hash: "b20a0ce3273406177885d8b1f53f4bad",
        },
        "18/declaration/body/6/argument/13/1/3": {
          content: {
            de: "<element:Plus></element:Plus> Neu hinzufügen",
            en: "<element:Plus></element:Plus> Add New",
            fr: "<element:Plus></element:Plus> Ajouter",
            nl: "<element:Plus></element:Plus> Nieuw toevoegen",
          },
          hash: "eee3560b4a6ab72f44029ee38dd4191c",
        },
        "18/declaration/body/6/argument/13/3/1/1/1/1": {
          content: {
            de: "Ausgabenübersicht",
            en: "Spending Overview",
            fr: "Aperçu des dépenses",
            nl: "Uitgavenoverzicht",
          },
          hash: "b6e315dd94a11c083db5dbeebf3e3e01",
        },
        "18/declaration/body/6/argument/13/3/1/1/1/3": {
          content: {
            de: "Ihre Ausgabenaufschlüsselung für diesen Monat",
            en: "Your spending breakdown for this month",
            fr: "Répartition de vos dépenses pour ce mois",
            nl: "Je uitgavenoverzicht voor deze maand",
          },
          hash: "461120431b081c446a8e41d951419a6c",
        },
        "18/declaration/body/6/argument/13/3/1/1/3/1/expression/alternate/1": {
          content: {
            de: "Keine Ausgabendaten für diesen Monat.",
            en: "No spending data for this month.",
            fr: "Aucune donnée de dépense pour ce mois.",
            nl: "Geen uitgavengegevens voor deze maand.",
          },
          hash: "303f23fd57e8728e48270abd7a50ca50",
        },
        "18/declaration/body/6/argument/13/3/1/5/1/1": {
          content: {
            de: "Finanzielle Ziele",
            en: "Financial Goals",
            fr: "Objectifs financiers",
            nl: "Financiële doelen",
          },
          hash: "7956b1b05f6cc75d5894998d5bdb6c7e",
        },
        "18/declaration/body/6/argument/13/3/1/5/1/3": {
          content: {
            de: "Ihre wichtigsten finanziellen Bestrebungen",
            en: "Your top financial aspirations",
            fr: "Vos principales aspirations financières",
            nl: "Je belangrijkste financiële ambities",
          },
          hash: "6b50997454fefa1e262f972ea2d12b93",
        },
        "18/declaration/body/6/argument/13/3/1/5/3/1/expression/alternate/1": {
          content: {
            de: "Keine aktiven Ziele anzuzeigen.",
            en: "No active goals to display.",
            fr: "Aucun objectif actif à afficher.",
            nl: "Geen actieve doelen om weer te geven.",
          },
          hash: "e4ae66f70786bd04bf199e0bec8af5ca",
        },
        "18/declaration/body/6/argument/13/3/1/5/3/1/expression/consequent/0/body/5/3":
          {
            content: {
              de: "Ziel: <function:formatCurrency/>",
              en: "Target: <function:formatCurrency/>",
              fr: "Cible : <function:formatCurrency/>",
              nl: "Doel: <function:formatCurrency/>",
            },
            hash: "eb04b8e4136017ae0fce9a9580224b0d",
          },
        "18/declaration/body/6/argument/13/3/1/5/3/1/expression/consequent/0/body/7/expression/right":
          {
            content: {
              de: "Sparen Sie ~<function:formatCurrency/> /Monat, um das Ziel bis <function:Date.toLocaleDateString/> zu erreichen",
              en: "Save ~<function:formatCurrency/> /month to reach by <function:Date.toLocaleDateString/>",
              fr: "Épargnez ~<function:formatCurrency/> /mois pour atteindre d'ici le <function:Date.toLocaleDateString/>",
              nl: "Spaar ~<function:formatCurrency/> /maand om te bereiken tegen <function:Date.toLocaleDateString/>",
            },
            hash: "7a0f45d2d80713e1ef16130a6fc08819",
          },
        "18/declaration/body/6/argument/13/3/1/5/3/3/expression/right": {
          content: {
            de: "Alle Ziele anzeigen <element:ArrowRight></element:ArrowRight>",
            en: "View All Goals <element:ArrowRight></element:ArrowRight>",
            fr: "Voir tous les objectifs <element:ArrowRight></element:ArrowRight>",
            nl: "Alle doelen bekijken <element:ArrowRight></element:ArrowRight>",
          },
          hash: "c1a3caa2fc7c0ffa05d70b0719046222",
        },
        "18/declaration/body/6/argument/13/3/5/1/1": {
          content: {
            de: "Letzte Aktivitäten",
            en: "Recent Activity",
            fr: "Activité récente",
            nl: "Recente activiteit",
          },
          hash: "d4b07b3b4faedbd20762ab8a665d979d",
        },
        "18/declaration/body/6/argument/13/3/5/1/3": {
          content: {
            de: "Ihre neuesten finanziellen Aktivitäten",
            en: "Your latest financial activities",
            fr: "Vos dernières activités financières",
            nl: "Je laatste financiële activiteiten",
          },
          hash: "169ce7e54531b999e5fd8a1a81c4bdb3",
        },
        "18/declaration/body/6/argument/13/3/5/1/5/1": {
          content: {
            de: "Alle anzeigen <element:ArrowUpRight></element:ArrowUpRight>",
            en: "View All <element:ArrowUpRight></element:ArrowUpRight>",
            fr: "Tout voir <element:ArrowUpRight></element:ArrowUpRight>",
            nl: "Alles bekijken <element:ArrowUpRight></element:ArrowUpRight>",
          },
          hash: "67628cd47055675071aeba7801d65891",
        },
        "18/declaration/body/6/argument/13/3/5/3/1/expression/alternate/1": {
          content: {
            de: "Keine aktuellen Transaktionen.",
            en: "No recent transactions.",
            fr: "Aucune transaction récente.",
            nl: "Geen recente transacties.",
          },
          hash: "40c71e50aa781dce38477792167b5d67",
        },
        "18/declaration/body/6/argument/13/5/1/1/1": {
          content: {
            de: "Alle Transaktionen",
            en: "All Transactions",
            fr: "Toutes les transactions",
            nl: "Alle transacties",
          },
          hash: "0788f9fe612abf449a2423d6d6ecde9f",
        },
        "18/declaration/body/6/argument/13/5/1/1/3": {
          content: {
            de: "Sehen und verwalten Sie alle Ihre finanziellen Aktivitäten.",
            en: "View and manage all your financial activities.",
            fr: "Consultez et gérez toutes vos activités financières.",
            nl: "Bekijk en beheer al je financiële activiteiten.",
          },
          hash: "a2d37efe579f239e64a7dfdc849ab1a3",
        },
        "18/declaration/body/6/argument/13/5/1/3/1/expression/alternate/1": {
          content: {
            de: "Keine Transaktionen gefunden.",
            en: "No transactions found.",
            fr: "Aucune transaction trouvée.",
            nl: "Geen transacties gevonden.",
          },
          hash: "01023131213960176684d7ad134e0c2d",
        },
        "18/declaration/body/6/argument/13/5/1/3/1/expression/consequent/1/1/1":
          {
            content: {
              de: "Datum",
              en: "Date",
              fr: "Date",
              nl: "Datum",
            },
            hash: "56f41c5d30a76295bb087b20b7bee4c3",
          },
        "18/declaration/body/6/argument/13/5/1/3/1/expression/consequent/1/1/3":
          {
            content: {
              de: "Beschreibung",
              en: "Description",
              fr: "Description",
              nl: "Omschrijving",
            },
            hash: "e17686a22ffad04cc7bb70524ed4478b",
          },
        "18/declaration/body/6/argument/13/5/1/3/1/expression/consequent/1/1/5":
          {
            content: {
              de: "Kategorie",
              en: "Category",
              fr: "Catégorie",
              nl: "Categorie",
            },
            hash: "1b0340cd175aa5a2be745a0d54908466",
          },
        "18/declaration/body/6/argument/13/5/1/3/1/expression/consequent/1/1/7":
          {
            content: {
              de: "Betrag",
              en: "Amount",
              fr: "Montant",
              nl: "Bedrag",
            },
            hash: "a0ae9c6ff98b57475cdce56f66066aa4",
          },
        "18/declaration/body/6/argument/13/7/1/1/1": {
          content: {
            de: "Budgets",
            en: "Budgets",
            fr: "Budgets",
            nl: "Budgetten",
          },
          hash: "8dcb6cddfd7b9a0ce4a4e908b86662f7",
        },
        "18/declaration/body/6/argument/13/7/1/1/3": {
          content: {
            de: "Verwalten Sie Ihre Ausgabenziele.",
            en: "Manage your spending targets.",
            fr: "Gérez vos objectifs de dépenses.",
            nl: "Beheer je uitgavendoelen.",
          },
          hash: "b97cee739f4a9d65570b2c9218762b77",
        },
        "18/declaration/body/6/argument/13/7/1/3/1/expression/alternate/1": {
          content: {
            de: "Noch keine Budgets eingerichtet. Erstellen Sie eines, um zu beginnen!",
            en: "No budgets set up yet. Create one to get started!",
            fr: "Aucun budget configuré pour l'instant. Créez-en un pour commencer !",
            nl: "Nog geen budgetten ingesteld. Maak er een aan om te beginnen!",
          },
          hash: "b7bca99de4a2559be4f6fe4f8a813d9c",
        },
        "18/declaration/body/6/argument/13/7/1/3/1/expression/consequent/1/expression/0/body/4/argument/3/1/1":
          {
            content: {
              de: "Ausgegeben: {spent}",
              en: "Spent: {spent}",
              fr: "Dépensé : {spent}",
              nl: "Uitgegeven: {spent}",
            },
            hash: "1c55d3262e63507077c94e7ab96d6482",
          },
        "18/declaration/body/6/argument/13/7/1/3/1/expression/consequent/1/expression/0/body/4/argument/3/1/3":
          {
            content: {
              de: "Budgetiert: {amount}",
              en: "Budgeted: {amount}",
              fr: "Budgété : {amount}",
              nl: "Gebudgetteerd: {amount}",
            },
            hash: "b821ef948eaafced8107fceebe0f8931",
          },
        "18/declaration/body/6/argument/13/7/1/3/1/expression/consequent/1/expression/0/body/4/argument/3/5/1":
          {
            content: {
              de: "<expression/>% verwendet",
              en: "<expression/>% Used",
              fr: "<expression/> % utilisé",
              nl: "<expression/>% gebruikt",
            },
            hash: "fb967ffe6c3270cbc5a421329692b91e",
          },
        "18/declaration/body/6/argument/13/7/1/3/1/expression/consequent/1/expression/0/body/4/argument/3/5/3":
          {
            content: {
              de: "Verbleibend: {remaining}",
              en: "Remaining: {remaining}",
              fr: "Restant : {remaining}",
              nl: "Resterend: {remaining}",
            },
            hash: "a4a91e733c32d29c6d3bae63213297cc",
          },
        "18/declaration/body/6/argument/13/7/1/3/1/expression/consequent/1/expression/0/body/4/argument/5":
          {
            content: {
              de: "Zeitraum: <expression/><expression/>",
              en: "Period: <expression/><expression/>",
              fr: "Période : <expression/><expression/>",
              nl: "Periode: <expression/><expression/>",
            },
            hash: "2d5b1392abc3f71f737af12065e86fff",
          },
        "18/declaration/body/6/argument/13/9/1/1/1": {
          content: {
            de: "Finanzielle Ziele",
            en: "Financial Goals",
            fr: "Objectifs financiers",
            nl: "Financiële doelen",
          },
          hash: "7956b1b05f6cc75d5894998d5bdb6c7e",
        },
        "18/declaration/body/6/argument/13/9/1/1/3": {
          content: {
            de: "Fortschritt bei Ihren finanziellen Zielen",
            en: "Progress towards your financial goals",
            fr: "Progression vers vos objectifs financiers",
            nl: "Voortgang richting je financiële doelen",
          },
          hash: "e25a759a044caec19f7372553f04acaf",
        },
        "18/declaration/body/6/argument/13/9/1/3/1": {
          content: {
            de: "<element:Target></element:Target> Zielfortschritt wird hier angezeigt",
            en: "<element:Target></element:Target> Goal progress will be displayed here",
            fr: "<element:Target></element:Target> La progression des objectifs sera affichée ici",
            nl: "<element:Target></element:Target> Doelvoortgang wordt hier weergegeven",
          },
          hash: "02333e2493f9d23ff58426489b31cad3",
        },
        "18/declaration/body/6/argument/17/1/1/1": {
          content: {
            de: "Letzte Transaktionen",
            en: "Recent Transactions",
            fr: "Transactions récentes",
            nl: "Recente transacties",
          },
          hash: "c1adc026ca419c2e105271ce9155f17a",
        },
        "18/declaration/body/6/argument/17/1/1/3": {
          content: {
            de: "Ihre neuesten finanziellen Aktivitäten",
            en: "Your latest financial activity",
            fr: "Votre activité financière récente",
            nl: "Je laatste financiële activiteit",
          },
          hash: "eed4220748c3b4fecc4226adfa21c6c2",
        },
        "18/declaration/body/6/argument/17/1/3/1/expression/alternate": {
          content: {
            de: "Keine aktuellen Transaktionen",
            en: "No recent transactions",
            fr: "Aucune transaction récente",
            nl: "Geen recente transacties",
          },
          hash: "cfdbfbd50e90bd2a25b3b1c94b26db39",
        },
        "18/declaration/body/6/argument/17/3/1/1": {
          content: {
            de: "Konten",
            en: "Accounts",
            fr: "Comptes",
            nl: "Rekeningen",
          },
          hash: "dbb9796c495f5e0f0de6f71bd32527ae",
        },
        "18/declaration/body/6/argument/17/3/1/3": {
          content: {
            de: "Verwalten Sie Ihr Geld",
            en: "Manage your money",
            fr: "Gérez votre argent",
            nl: "Beheer je geld",
          },
          hash: "b39a4cbe43b076697f04d19b4e4fbde3",
        },
        "18/declaration/body/6/argument/17/3/3/1/expression/alternate": {
          content: {
            de: "Noch keine Konten hinzugefügt",
            en: "No accounts added yet",
            fr: "Aucun compte ajouté pour l'instant",
            nl: "Nog geen rekeningen toegevoegd",
          },
          hash: "dd18ea2628056db2f0235c0e4ac1dabf",
        },
        "18/declaration/body/6/argument/5/1/3/1": {
          content: {
            de: "Gesamtguthaben",
            en: "Total Balance",
            fr: "Solde total",
            nl: "Totaal saldo",
          },
          hash: "fd1144e816cfaf16c396da0540d57f8c",
        },
        "18/declaration/body/6/argument/5/1/5/3/5": {
          content: {
            de: "im Vergleich zum Vormonat",
            en: "from last month",
            fr: "par rapport au mois dernier",
            nl: "ten opzichte van vorige maand",
          },
          hash: "fa633b17714eb94793f336520083e4b4",
        },
        "18/declaration/body/6/argument/5/3/3/1": {
          content: {
            de: "Monatliches Einkommen",
            en: "Monthly Income",
            fr: "Revenus mensuels",
            nl: "Maandelijks inkomen",
          },
          hash: "f0d770bdda6bc1e8847686d1c8ac8997",
        },
        "18/declaration/body/6/argument/5/3/5/3/5": {
          content: {
            de: "im Vergleich zum Vormonat",
            en: "from last month",
            fr: "par rapport au mois dernier",
            nl: "ten opzichte van vorige maand",
          },
          hash: "fa633b17714eb94793f336520083e4b4",
        },
        "18/declaration/body/6/argument/5/5/3/1": {
          content: {
            de: "Monatliche Ausgaben",
            en: "Monthly Expenses",
            fr: "Dépenses mensuelles",
            nl: "Maandelijkse uitgaven",
          },
          hash: "cfd957ffdc837c0851bdc307a220f692",
        },
        "18/declaration/body/6/argument/5/5/5/3/5": {
          content: {
            de: "im Vergleich zum Vormonat",
            en: "from last month",
            fr: "par rapport au mois dernier",
            nl: "ten opzichte van vorige maand",
          },
          hash: "fa633b17714eb94793f336520083e4b4",
        },
        "18/declaration/body/6/argument/5/7/3/1": {
          content: {
            de: "Sparquote",
            en: "Savings Rate",
            fr: "Taux d'épargne",
            nl: "Spaarpercentage",
          },
          hash: "32bbceface3004ce15d7fc932f366230",
        },
        "18/declaration/body/6/argument/5/7/5/3": {
          content: {
            de: "des Einkommens in diesem Monat gespart",
            en: "of income saved this month",
            fr: "des revenus épargnés ce mois-ci",
            nl: "van inkomen gespaard deze maand",
          },
          hash: "5a2050cf5d0a88f4b6bcce536109de73",
        },
        "18/declaration/body/6/argument/9/1/1/1": {
          content: {
            de: "Zusätzliche Kennzahlen",
            en: "Additional Metrics",
            fr: "Métriques supplémentaires",
            nl: "Aanvullende metrieken",
          },
          hash: "05b37a49ec9ea48ad87362b89aed114a",
        },
        "18/declaration/body/6/argument/9/1/1/3": {
          content: {
            de: "Indikatoren für finanzielle Gesundheit",
            en: "Financial health indicators",
            fr: "Indicateurs de santé financière",
            nl: "Indicatoren voor financiële gezondheid",
          },
          hash: "2d6fbb6070df9771eec8ffda6253089c",
        },
        "18/declaration/body/6/argument/9/1/3/1/1/1/1": {
          content: {
            de: "Netto-Cashflow",
            en: "Net Cash Flow",
            fr: "Flux de trésorerie net",
            nl: "Netto cashflow",
          },
          hash: "821ffc060c7a8d3a051a03eb8d142d7a",
        },
        "18/declaration/body/6/argument/9/1/3/1/1/3/1": {
          content: {
            de: "Einnahmen-Ausgaben-Verhältnis",
            en: "Income to Expense Ratio",
            fr: "Ratio revenus/dépenses",
            nl: "Inkomsten-uitgavenratio",
          },
          hash: "36ed94036431e4e57410e7e31fdebae9",
        },
        "18/declaration/body/6/argument/9/1/3/1/1/3/3": {
          content: {
            de: "<function:incomeToExpenseRatio.toFixed/> : 1",
            en: "<function:incomeToExpenseRatio.toFixed/> : 1",
            fr: "<function:incomeToExpenseRatio.toFixed/> : 1",
            nl: "<function:incomeToExpenseRatio.toFixed/> : 1",
          },
          hash: "e730cea5b7ad84dd3ef790d0bcdbefb3",
        },
        "18/declaration/body/6/argument/9/5/1/1": {
          content: {
            de: "Ziele & Budgets",
            en: "Goals & Budgets",
            fr: "Objectifs et budgets",
            nl: "Doelen & budgetten",
          },
          hash: "de7106b322640ce5f3dfcd4b302f275a",
        },
        "18/declaration/body/6/argument/9/5/1/3": {
          content: {
            de: "Verfolgen Sie Ihre aktiven Finanzpläne",
            en: "Track your active financial plans",
            fr: "Suivez vos plans financiers actifs",
            nl: "Volg je actieve financiële plannen",
          },
          hash: "ee8a6c2a451bafc52e88e26253972488",
        },
        "18/declaration/body/6/argument/9/5/3/1/1/3/1": {
          content: {
            de: "Aktive finanzielle Ziele",
            en: "Active Financial Goals",
            fr: "Objectifs financiers actifs",
            nl: "Actieve financiële doelen",
          },
          hash: "318b8eb3b4fb97ebb1b5e2cfcf32376b",
        },
        "18/declaration/body/6/argument/9/5/3/1/1/3/3": {
          content: {
            de: "Ihre aktuellen Bestrebungen",
            en: "Your current aspirations",
            fr: "Vos aspirations actuelles",
            nl: "Je huidige ambities",
          },
          hash: "875fc705d64229debc3d1b374d969862",
        },
        "18/declaration/body/6/argument/9/5/3/3/1/3/1": {
          content: {
            de: "Aktive Budgets",
            en: "Active Budgets",
            fr: "Budgets actifs",
            nl: "Actieve budgetten",
          },
          hash: "bcf4e0d60b63efb4f87b74cc39a783dc",
        },
        "18/declaration/body/6/argument/9/5/3/3/1/3/3": {
          content: {
            de: "Ihre aktuellen Ausgabenpläne",
            en: "Your current spending plans",
            fr: "Vos plans de dépenses actuels",
            nl: "Je huidige uitgavenplannen",
          },
          hash: "b71b34228efef87bee572d20f59692de",
        },
      },
    },
    "features/dashboard/components/welcome-dashboard.tsx": {
      entries: {
        "14/declaration/body/8/argument/7/1/1/1/3": {
          content: {
            de: "Budgetplaner",
            en: "Budget Tracker",
            fr: "Suivi de budget",
            nl: "Budgettracker",
          },
          hash: "cc32b4a6755a4197ba7fa3e043e6f229",
        },
        "14/declaration/body/8/argument/7/1/3/1-aria-label": {
          content: {
            de: "Design umschalten",
            en: "Toggle theme",
            fr: "Changer de thème",
            nl: "Thema schakelen",
          },
          hash: "dd03ee6b83901fc440f90236483dafc5",
        },
        "14/declaration/body/8/argument/7/1/3/1/5": {
          content: {
            de: "Design umschalten",
            en: "Toggle theme",
            fr: "Changer de thème",
            nl: "Thema schakelen",
          },
          hash: "dd03ee6b83901fc440f90236483dafc5",
        },
        "14/declaration/body/8/argument/7/1/3/3/3/5/1/3": {
          content: {
            de: "Einstellungen",
            en: "Settings",
            fr: "Paramètres",
            nl: "Instellingen",
          },
          hash: "8df6777277469c1fd88cc18dde2f1cc3",
        },
        "14/declaration/body/8/argument/7/1/3/3/3/9/3": {
          content: {
            de: "Abmelden",
            en: "Log out",
            fr: "Déconnexion",
            nl: "Uitloggen",
          },
          hash: "9a236bb8f8bec867ec0d0950d38bcc71",
        },
        "14/declaration/body/8/argument/9/11/1/5/1/3": {
          content: {
            de: "Bereit loszulegen?",
            en: "Ready to get started?",
            fr: "Prêt à commencer ?",
            nl: "Klaar om te beginnen?",
          },
          hash: "e52155f48587a6a96b7c1970abbcc5e8",
        },
        "14/declaration/body/8/argument/9/11/1/5/1/5": {
          content: {
            de: "Wähle oben deinen bevorzugten Finanzmodus, um auf dein Dashboard zuzugreifen, oder erkunde beide Optionen, um zu sehen, was am besten für dich funktioniert.",
            en: "Choose your preferred finance mode above to access your dashboard, or explore both options to see what works best.",
            fr: "Choisissez votre mode de gestion financière préféré ci-dessus pour accéder à votre tableau de bord, ou explorez les deux options pour voir ce qui vous convient le mieux.",
            nl: "Kies hierboven je gewenste financiële modus om toegang te krijgen tot je dashboard, of verken beide opties om te zien wat het beste werkt.",
          },
          hash: "56daf8fe64ddc18e4488918d23fa5bb6",
        },
        "14/declaration/body/8/argument/9/11/1/5/5/1/3": {
          content: {
            de: "Intelligente Einblicke",
            en: "Smart Insights",
            fr: "Analyses intelligentes",
            nl: "Slimme inzichten",
          },
          hash: "656173635ec9f37aed2f48addc24f968",
        },
        "14/declaration/body/8/argument/9/11/1/5/5/3/3": {
          content: {
            de: "Zielerreichung",
            en: "Goal Achievement",
            fr: "Réalisation d'objectifs",
            nl: "Doelrealisatie",
          },
          hash: "f3bda9ccbe9abbb007101ecfb7960e9a",
        },
        "14/declaration/body/8/argument/9/11/1/5/5/5/3": {
          content: {
            de: "Sichere Plattform",
            en: "Secure Platform",
            fr: "Plateforme sécurisée",
            nl: "Veilig platform",
          },
          hash: "339622e4147d940085cd76061fb6e3d9",
        },
        "14/declaration/body/8/argument/9/3/1/5": {
          content: {
            de: "Willkommen zurück, <element:span><function:getFirstName/>!</element:span> 👋",
            en: "Welcome back, <element:span><function:getFirstName/>!</element:span>  👋",
            fr: "Bon retour, <element:span><function:getFirstName/>!</element:span> 👋",
            nl: "Welkom terug, <element:span><function:getFirstName/>!</element:span> 👋",
          },
          hash: "e82b0e20a35e8f4a21dacb02fdc7fdcb",
        },
        "14/declaration/body/8/argument/9/3/1/7": {
          content: {
            de: "Wähle, wie du heute deine Finanzen verwalten möchtest. Ob <element:span>persönliche Budgetplanung</element:span> oder <element:span>Familienfinanzplanung</element:span>, wir haben für dich die passende Lösung.",
            en: "Choose how you'd like to manage your finances today. Whether it's <element:span>personal budgeting</element:span>  or <element:span>family financial planning</element:span> , we've got you covered.",
            fr: "Choisissez comment vous souhaitez gérer vos finances aujourd'hui. Que ce soit pour <element:span>la budgétisation personnelle</element:span> ou <element:span>la planification financière familiale</element:span>, nous avons ce qu'il vous faut.",
            nl: "Kies hoe je vandaag je financiën wilt beheren. Of het nu gaat om <element:span>persoonlijke budgettering</element:span> of <element:span>financiële gezinsplanning</element:span>, wij hebben alles voor je geregeld.",
          },
          hash: "27949fe2720dce83af1838cca7e03705",
        },
        "14/declaration/body/8/argument/9/3/5/1/3": {
          content: {
            de: "Intelligente Einblicke",
            en: "Smart Insights",
            fr: "Analyses intelligentes",
            nl: "Slimme inzichten",
          },
          hash: "97db1604f00977f3a13b40273a2443e6",
        },
        "14/declaration/body/8/argument/9/3/5/3/3": {
          content: {
            de: "Zielverfolgung",
            en: "Goal Tracking",
            fr: "Suivi des objectifs",
            nl: "Doeltracking",
          },
          hash: "fd6882b46bd0c5b9bfa51eb00e401b0c",
        },
        "14/declaration/body/8/argument/9/3/5/5/3": {
          content: {
            de: "Sicher & Privat",
            en: "Secure & Private",
            fr: "Sécurisé & privé",
            nl: "Veilig & privé",
          },
          hash: "0e7bd2cbe4ec73f9b8c14cf48c4df572",
        },
        "14/declaration/body/8/argument/9/7/3/1/5/1/1/3/1": {
          content: {
            de: "Persönliche Finanzen",
            en: "Personal Finance",
            fr: "Finances personnelles",
            nl: "Persoonlijke financiën",
          },
          hash: "080589371b9b73a323208d9f78cf1406",
        },
        "14/declaration/body/8/argument/9/7/3/1/5/1/1/3/3": {
          content: {
            de: "Verwalte deine individuellen Finanzen",
            en: "Manage your individual finances",
            fr: "Gérez vos finances individuelles",
            nl: "Beheer je individuele financiën",
          },
          hash: "febbc53d0d5de259aa8f9d5c29b2dedc",
        },
        "14/declaration/body/8/argument/9/7/3/1/5/3/1": {
          content: {
            de: "Übernimm die Kontrolle über dein persönliches Budget, verfolge Ausgaben und erreiche deine finanziellen Ziele mit leistungsstarken Analysen.",
            en: "Take control of your personal budget, track expenses, and achieve your financial goals with powerful analytics.",
            fr: "Prenez le contrôle de votre budget personnel, suivez vos dépenses et atteignez vos objectifs financiers grâce à des analyses puissantes.",
            nl: "Neem controle over je persoonlijke budget, volg uitgaven en bereik je financiële doelen met krachtige analyses.",
          },
          hash: "42760134313d1d78a38484215fd77cc9",
        },
        "14/declaration/body/8/argument/9/7/3/1/5/3/3/1/3": {
          content: {
            de: "Budgetverfolgung",
            en: "Budget Tracking",
            fr: "Suivi budgétaire",
            nl: "Budgetbeheer",
          },
          hash: "cec4fb53a42d8f2c4b2d21dfa2429ff4",
        },
        "14/declaration/body/8/argument/9/7/3/1/5/3/3/3/3": {
          content: {
            de: "Ausgabenanalyse",
            en: "Expense Analytics",
            fr: "Analyse des dépenses",
            nl: "Uitgavenanalyse",
          },
          hash: "2acbbb31bfe9b07e503ffd32cb2770ad",
        },
        "14/declaration/body/8/argument/9/7/3/1/5/3/3/5/3": {
          content: {
            de: "Finanzziele",
            en: "Financial Goals",
            fr: "Objectifs financiers",
            nl: "Financiële doelen",
          },
          hash: "eba8a445f85b5cf90350061882aaa766",
        },
        "14/declaration/body/8/argument/9/7/3/1/5/3/3/7/3": {
          content: {
            de: "Wachstumseinblicke",
            en: "Growth Insights",
            fr: "Perspectives de croissance",
            nl: "Groei-inzichten",
          },
          hash: "9763c0160b6575fd89c2aab1d83b94c7",
        },
        "14/declaration/body/8/argument/9/7/3/1/5/3/5/1": {
          content: {
            de: "Persönliche Finanzen öffnen<element:ArrowRight></element:ArrowRight>",
            en: "Access Personal Finance<element:ArrowRight></element:ArrowRight>",
            fr: "Accéder aux finances personnelles<element:ArrowRight></element:ArrowRight>",
            nl: "Toegang tot persoonlijke financiën<element:ArrowRight></element:ArrowRight>",
          },
          hash: "e2e7de88a54f1e70517102ec82cce3b3",
        },
        "14/declaration/body/8/argument/9/7/7/1/5/1/1/3/1": {
          content: {
            de: "Familienfinanzen",
            en: "Family Finance",
            fr: "Finances familiales",
            nl: "Gezinsfinanciën",
          },
          hash: "b060694d232613857788231d5273b132",
        },
        "14/declaration/body/8/argument/9/7/7/1/5/1/1/3/3": {
          content: {
            de: "Gemeinsam an Familienbudgets arbeiten",
            en: "Collaborate on family budgets",
            fr: "Collaborez sur les budgets familiaux",
            nl: "Samenwerken aan gezinsbudgetten",
          },
          hash: "1a2eede07f0b6dc6ada7602708c1faf2",
        },
        "14/declaration/body/8/argument/9/7/7/1/5/3/1": {
          content: {
            de: "Arbeite zusammen mit Familienmitgliedern, um gemeinsame Ausgaben, Ersparnisse und kollaborative Finanzplanung zu verwalten.",
            en: "Work together with family members to manage shared expenses, savings, and collaborative financial planning.",
            fr: "Travaillez ensemble avec les membres de votre famille pour gérer les dépenses partagées, l'épargne et la planification financière collaborative.",
            nl: "Werk samen met gezinsleden om gedeelde uitgaven, spaargeld en gezamenlijke financiële planning te beheren.",
          },
          hash: "78d58348e6f27bd7e4b8c7e1acfbc361",
        },
        "14/declaration/body/8/argument/9/7/7/1/5/3/3/1/3": {
          content: {
            de: "Gemeinsame Budgets",
            en: "Shared Budgets",
            fr: "Budgets partagés",
            nl: "Gedeelde budgetten",
          },
          hash: "a151c1dc4fcfea86c47fabe4a109db1f",
        },
        "14/declaration/body/8/argument/9/7/7/1/5/3/3/3/3": {
          content: {
            de: "Sichere Zusammenarbeit",
            en: "Safe Collaboration",
            fr: "Collaboration sécurisée",
            nl: "Veilige samenwerking",
          },
          hash: "e384ec2acd526fd095b7722dda9767cc",
        },
        "14/declaration/body/8/argument/9/7/7/1/5/3/3/5/3": {
          content: {
            de: "Familienziele",
            en: "Family Goals",
            fr: "Objectifs familiaux",
            nl: "Gezinsdoelen",
          },
          hash: "0f3aac44c34238577ba083a6e5b0d6c7",
        },
        "14/declaration/body/8/argument/9/7/7/1/5/3/3/7/3": {
          content: {
            de: "Gemeinsame Planung",
            en: "Joint Planning",
            fr: "Planification conjointe",
            nl: "Gezamenlijke planning",
          },
          hash: "2e440fd32cff64c5fa38f4f2bd383625",
        },
        "14/declaration/body/8/argument/9/7/7/1/5/3/5/1": {
          content: {
            de: "Familienfinanzen öffnen<element:ArrowRight></element:ArrowRight>",
            en: "Access Family Finance<element:ArrowRight></element:ArrowRight>",
            fr: "Accéder aux finances familiales<element:ArrowRight></element:ArrowRight>",
            nl: "Toegang tot gezinsfinanciën<element:ArrowRight></element:ArrowRight>",
          },
          hash: "8211ac2580f18db3a9ca45242802ea01",
        },
      },
    },
    "shared/components/layout/header.tsx": {
      entries: {
        "13/declaration/body/11/argument/1/1/1-aria-label": {
          content: {
            de: "Menü umschalten",
            en: "Toggle menu",
            fr: "Basculer le menu",
            nl: "Menu schakelen",
          },
          hash: "29dea3e0b6238874f8c7a27619df8e36",
        },
        "13/declaration/body/11/argument/1/1/3/1/1": {
          content: {
            de: "Budget Tracker",
            en: "Budget Tracker",
            fr: "Budget Tracker",
            nl: "Budget Tracker",
          },
          hash: "c970d03c09b541a19ea49ba831c210ee",
        },
        "13/declaration/body/11/argument/1/1/3/3/expression/right/3/3/1": {
          content: {
            de: "Modus wechseln",
            en: "Switch Mode",
            fr: "Changer de mode",
            nl: "Modus wisselen",
          },
          hash: "6e439756732a61aab5a183e60476fded",
        },
        "13/declaration/body/11/argument/1/1/3/3/expression/right/3/3/5/1/3": {
          content: {
            de: "Dashboard-Startseite",
            en: "Dashboard Home",
            fr: "Tableau de bord",
            nl: "Dashboard home",
          },
          hash: "45b6767c6ac7f6d004a186ffe5cd157b",
        },
        "13/declaration/body/11/argument/1/1/3/3/expression/right/3/3/7/1/3": {
          content: {
            de: "Persönliche Finanzen",
            en: "Personal Finance",
            fr: "Finances personnelles",
            nl: "Persoonlijke financiën",
          },
          hash: "315f4c7a6e838e49e59c7306f2f6f652",
        },
        "13/declaration/body/11/argument/1/1/3/3/expression/right/3/3/9/1/3": {
          content: {
            de: "Familienfinanzen",
            en: "Family Finance",
            fr: "Finances familiales",
            nl: "Gezinsfinanciën",
          },
          hash: "2325109d933119617af7f021735e3159",
        },
        "13/declaration/body/11/argument/1/3/3-aria-label": {
          content: {
            de: "Design umschalten",
            en: "Toggle theme",
            fr: "Changer de thème",
            nl: "Thema schakelen",
          },
          hash: "dd03ee6b83901fc440f90236483dafc5",
        },
        "13/declaration/body/11/argument/1/3/3/5": {
          content: {
            de: "Design umschalten",
            en: "Toggle theme",
            fr: "Changer de thème",
            nl: "Thema schakelen",
          },
          hash: "dd03ee6b83901fc440f90236483dafc5",
        },
        "13/declaration/body/11/argument/1/3/5/3/5/1/3": {
          content: {
            de: "Profil",
            en: "Profile",
            fr: "Profil",
            nl: "Profiel",
          },
          hash: "d7878693f91303a438852d617f6d35df",
        },
        "13/declaration/body/11/argument/1/3/5/3/9/3": {
          content: {
            de: "Abmelden",
            en: "Log out",
            fr: "Déconnexion",
            nl: "Uitloggen",
          },
          hash: "9a236bb8f8bec867ec0d0950d38bcc71",
        },
      },
    },
    "shared/components/layout/sidebar.tsx": {
      entries: {
        "12/declaration/body/16/argument/5/1/1/1/expression/right": {
          content: {
            de: "Budget",
            en: "Budget",
            fr: "Budget",
            nl: "Budget",
          },
          hash: "034e666c2c0205f946fc21583e9609c8",
        },
        "12/declaration/body/16/argument/5/1/1/3/1/3": {
          content: {
            de: "<expression/> Seitenleiste",
            en: "<expression/> sidebar",
            fr: "<expression/> barre latérale",
            nl: "<expression/> zijbalk",
          },
          hash: "10b1250c25dd4d336317f8b5651e519b",
        },
        "12/declaration/body/16/argument/5/1/3/1/3/1/expression/right/3": {
          content: {
            de: "Aktiv",
            en: "Active",
            fr: "Actif",
            nl: "Actief",
          },
          hash: "ad507102be7785a5ed1c0a6d4c074345",
        },
        "12/declaration/body/16/argument/5/1/3/1/7/expression/right/1/expression/right":
          {
            content: {
              de: "Gruppennavigation<element:span>Aktiv</element:span>",
              en: "Group Navigation<element:span>Active</element:span>",
              fr: "Navigation de groupe<element:span>Actif</element:span>",
              nl: "Groepsnavigatie<element:span>Actief</element:span>",
            },
            hash: "b1f971bbc1f752a6fb182d32636900db",
          },
        "12/declaration/body/16/argument/5/1/5/expression/right/1/3/1": {
          content: {
            de: "Benutzerkonto",
            en: "User Account",
            fr: "Compte utilisateur",
            nl: "Gebruikersaccount",
          },
          hash: "25ed52adc5d3cf00a7a61a4e236f651b",
        },
        "12/declaration/body/16/argument/5/1/5/expression/right/1/3/3": {
          content: {
            de: "Kostenloser Plan",
            en: "Free Plan",
            fr: "Forfait gratuit",
            nl: "Gratis abonnement",
          },
          hash: "7976d90f0b331f8b93bc869911b556ee",
        },
      },
    },
    "shared/components/ui/date-picker.tsx": {
      entries: {
        "8/declaration/body/0/argument/1/1/3/expression/alternate": {
          content: {
            de: "Datum auswählen",
            en: "Pick a date",
            fr: "Choisir une date",
            nl: "Kies een datum",
          },
          hash: "78a7959a5c1094c4f6e95523dd49a45a",
        },
      },
    },
    "shared/components/ui/dialog.tsx": {
      entries: {
        "10/0/init/0/body/3/3/3": {
          content: {
            de: "Schließen",
            en: "Close",
            fr: "Fermer",
            nl: "Sluiten",
          },
          hash: "2c2e22f8424a1031de89063bd0022e16",
        },
      },
    },
    "shared/components/ui/language-switcher.tsx": {
      entries: {
        "7/declaration/body/4/argument/1/1-aria-label": {
          content: {
            de: "Sprache ändern",
            en: "Change language",
            fr: "Changer de langue",
            nl: "Taal wijzigen",
          },
          hash: "53bea3a813015ca3bf2e4cc6a269fb74",
        },
        "7/declaration/body/4/argument/3/1": {
          content: {
            de: "Language / Taal / Sprache / Langue",
            en: "Language / Taal / Sprache / Langue",
            fr: "Language / Taal / Sprache / Langue",
            nl: "Language / Taal / Sprache / Langue",
          },
          hash: "19f85118df3744e40a55e99855d5c966",
        },
      },
    },
    "shared/components/ui/sheet.tsx": {
      entries: {
        "13/0/init/0/body/3/3/3": {
          content: {
            de: "Schließen",
            en: "Close",
            fr: "Fermer",
            nl: "Sluiten",
          },
          hash: "2c2e22f8424a1031de89063bd0022e16",
        },
      },
    },
  },
};
