"use client";

import { useState, useEffect } from "react";
import { LocaleSwitcher } from "lingo.dev/react/client";
import { Button } from "@/shared/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/shared/components/ui/dropdown-menu";
import { Languages, Check } from "lucide-react";

// Language configuration
const languages = [
  { code: "en", name: "English", flag: "🇺🇸" },
  { code: "nl", name: "Nederlands", flag: "🇳🇱" },
  { code: "de", name: "<PERSON><PERSON><PERSON>", flag: "🇩🇪" },
  { code: "fr", name: "Français", flag: "🇫🇷" },
];

interface LanguageSwitcherProps {
  variant?: "icon" | "full";
  className?: string;
}

export function LanguageSwitcher({ 
  variant = "icon", 
  className = "" 
}: LanguageSwitcherProps) {
  const [currentLocale, setCurrentLocale] = useState("en");

  useEffect(() => {
    // Get current locale from URL or localStorage
    const locale = window.location.pathname.split('/')[1];
    if (languages.some(lang => lang.code === locale)) {
      setCurrentLocale(locale);
    }
  }, []);

  const currentLanguage = languages.find(lang => lang.code === currentLocale) || languages[0];

  if (variant === "full") {
    // Use Lingo's built-in LocaleSwitcher for full functionality
    return (
      <div className={className}>
        <LocaleSwitcher 
          locales={["en", "nl", "de", "fr"]}
          className="flex items-center gap-2 px-3 py-2 text-sm rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground"
        />
      </div>
    );
  }

  // Custom icon-based switcher
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon"
          className={className}
          aria-label="Change language"
        >
          <Languages className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuLabel>Language / Taal / Sprache / Langue</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => {
              // Navigate to the new locale
              const currentPath = window.location.pathname;
              const pathWithoutLocale = currentPath.replace(/^\/[a-z]{2}/, '') || '/';
              const newPath = language.code === 'en' 
                ? pathWithoutLocale 
                : `/${language.code}${pathWithoutLocale}`;
              window.location.href = newPath;
            }}
            className="flex items-center justify-between"
          >
            <div className="flex items-center gap-2">
              <span className="text-lg">{language.flag}</span>
              <span>{language.name}</span>
            </div>
            {currentLocale === language.code && (
              <Check className="h-4 w-4" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
