import type { NextConfig } from "next";
import lingoCompiler from "lingo.dev/compiler";

const nextConfig: NextConfig = {
  /* config options here */
};

export default lingoCompiler.next({
  sourceLocale: "en",
  targetLocales: ["nl", "de", "fr"], // Dutch, German, French (English is source)
  models: "lingo.dev", // Using Lingo.dev Engine for AI translations
  sourceRoot: "src", // Our source code is in src directory
  lingoDir: "lingo", // Translation files directory
  rsc: true, // Enable React Server Components support
  debug: true, // Enable debug logging to see what's happening
})(nextConfig);
