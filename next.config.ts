import type { NextConfig } from "next";
import lingoCompiler from "lingo.dev/compiler";

const nextConfig: NextConfig = {
  /* config options here */
};

export default lingoCompiler.next({
  sourceLocale: "en",
  targetLocales: ["nl", "de", "fr"], // Dutch, German, French (English is source)
  models: {
    "*:*": "groq:llama-3.3-70b-versatile", // Using GROQ as alternative
  },
  sourceRoot: "src", // Our source code is in src directory
  lingoDir: "lingo", // Translation files directory
  rsc: true, // Enable React Server Components support
  debug: false, // Enable debug logging in development if needed
})(nextConfig);
